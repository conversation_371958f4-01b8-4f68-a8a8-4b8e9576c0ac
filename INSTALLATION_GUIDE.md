# Agenda Generation Service - Installation Guide

This guide provides step-by-step instructions for installing and running the Agenda Generation Service on Ubuntu and Amazon Linux servers, with support for both GPU and non-GPU configurations.

## Table of Contents
- [Prerequisites](#prerequisites)
- [System Requirements](#system-requirements)
- [Installation Options](#installation-options)
  - [Option 1: Docker Installation (Recommended)](#option-1-docker-installation-recommended)
  - [Option 2: Manual Installation](#option-2-manual-installation)
- [GPU Server Setup](#gpu-server-setup)
- [Configuration](#configuration)
- [Running the Service](#running-the-service)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### For Ubuntu Servers
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git build-essential
```

### For Amazon Linux Servers
```bash
# Update system packages
sudo yum update -y

# Install essential packages
sudo yum groupinstall -y "Development Tools"
sudo yum install -y curl wget git
```

## System Requirements

### Minimum Requirements
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 20GB free space
- **OS**: Ubuntu 18.04+ or Amazon Linux 2+

### Recommended Requirements (with GPU)
- **CPU**: 8+ cores
- **RAM**: 16GB+
- **GPU**: NVIDIA GPU with 8GB+ VRAM
- **Storage**: 50GB+ free space
- **CUDA**: 11.8+ (for GPU support)

## Installation Options

## Option 1: Docker Installation (Recommended)

### Step 1: Install Docker

#### Ubuntu
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Amazon Linux
```bash
# Install Docker
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### Step 2: Clone Repository
```bash
git clone <repository-url>
cd agenda-generation/agenda-service
```

### Step 3: Configure Environment Variables

#### For Agenda Generator Service
```bash
# Copy environment template
cp agenda_generator/.env.example agenda_generator/.env

# Edit the environment file
nano agenda_generator/.env
```

Required environment variables for `agenda_generator/.env`:
```env
# RabbitMQ Configuration
MQ_HOST=host.docker.internal
MQ_PORT=5672
MQ_ROUTE_KEY=AGENDA_SERVICE
MQ_CLIMAS_ODOO_ROUTE_KEY=YOUR_ODOO_ROUTE_KEY
MQ_USERNAME=guest
MQ_PASSWORD=guest

# API Keys (obtain from respective services)
YOUTUBE_API_KEY=your_youtube_api_key
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
NEWS_API_KEY=your_news_api_key
```

#### For Cache Server
```bash
# Create cache server environment file
nano climas_agenda_cacheserver/.env
```

Required environment variables for `climas_agenda_cacheserver/.env`:
```env
# MongoDB Configuration
MONGO_URI=mongodb://mongodb:27017/
MONGODB=agenda_cache_db
```

### Step 4: Choose Deployment Configuration

#### For Non-GPU Servers
```bash
# Use the standard docker-compose file
docker-compose up -d --build
```

#### For GPU Servers
```bash
# Copy GPU configuration
cp docker-compose.cloud.gpu.yml.example docker-compose.gpu.yml

# Run with GPU support
docker-compose -f docker-compose.gpu.yml up -d --build
```

## Option 2: Manual Installation

### Step 1: Install Python and Dependencies

#### Ubuntu
```bash
# Install Python 3.9+
sudo apt install -y python3.9 python3.9-dev python3-pip python3.9-venv

# Install system dependencies
sudo apt install -y gcc libgl1 libglib2.0-0 libsm6 libxext6 libxrender-dev libssl-dev ffmpeg
```

#### Amazon Linux
```bash
# Install Python 3.9+
sudo yum install -y python39 python39-devel python39-pip

# Install system dependencies
sudo yum install -y gcc mesa-libGL glib2 libSM libXext libXrender openssl-devel ffmpeg
```

### Step 2: Set Up Virtual Environment
```bash
# Create virtual environment
python3.9 -m venv agenda_env
source agenda_env/bin/activate

# Upgrade pip
pip install --upgrade pip
```

### Step 3: Install Python Dependencies
```bash
# Navigate to agenda generator directory
cd agenda_generator

# Install spaCy and NLTK first
pip install spacy==3.7.5 nltk==3.9.1

# Download language models
python -m spacy download en_core_web_lg
python -m spacy download en_core_web_md
python -m nltk.downloader punkt punkt_tab vader_lexicon stopwords

# Install remaining dependencies
pip install -r requirements.txt

# Pre-download transformers models
python -c "from transformers import GPT2LMHeadModel, GPT2Tokenizer; GPT2LMHeadModel.from_pretrained('gpt2-medium'); GPT2Tokenizer.from_pretrained('gpt2-medium')"
```

### Step 4: Install MongoDB
#### Ubuntu
```bash
# Import MongoDB public key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Install MongoDB
sudo apt update
sudo apt install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### Amazon Linux
```bash
# Create MongoDB repository file
sudo tee /etc/yum.repos.d/mongodb-org-6.0.repo << EOF
[mongodb-org-6.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/amazon/2/mongodb-org/6.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-6.0.asc
EOF

# Install MongoDB
sudo yum install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

### Step 5: Install RabbitMQ
#### Ubuntu
```bash
sudo apt install -y rabbitmq-server
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
```

#### Amazon Linux
```bash
sudo yum install -y rabbitmq-server
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
```

## GPU Server Setup

### Step 1: Install NVIDIA Drivers
#### Ubuntu
```bash
# Install NVIDIA drivers
sudo apt install -y nvidia-driver-470

# Reboot system
sudo reboot
```

#### Amazon Linux
```bash
# Install NVIDIA drivers
sudo yum install -y nvidia-driver-latest-dkms

# Reboot system
sudo reboot
```

### Step 2: Install NVIDIA Container Toolkit
```bash
# Add NVIDIA package repository
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# Install nvidia-container-toolkit
sudo apt update
sudo apt install -y nvidia-container-toolkit

# Restart Docker
sudo systemctl restart docker
```

### Step 3: Install CUDA (for manual installation)
```bash
# Download and install CUDA 11.8
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_520.61.05_linux.run
sudo sh cuda_11.8.0_520.61.05_linux.run

# Add CUDA to PATH
echo 'export PATH=/usr/local/cuda/bin:$PATH' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

### Step 4: Install PyTorch with CUDA Support (for manual installation)
```bash
# Install PyTorch with CUDA 11.8 support
pip install torch==2.0.1+cu118 torchvision==0.15.2+cu118 -f https://download.pytorch.org/whl/torch_stable.html

# Install CuPy for additional GPU acceleration
pip install cupy-cuda11x
```

## Configuration

### Environment Variables Setup
Ensure all required environment variables are properly configured:

1. **API Keys**: Obtain from respective services:
   - YouTube API Key: [Google Cloud Console](https://console.cloud.google.com/)
   - Twitter Bearer Token: [Twitter Developer Portal](https://developer.twitter.com/)
   - News API Key: [NewsAPI](https://newsapi.org/)

2. **RabbitMQ Configuration**: Update connection details if using external RabbitMQ

3. **MongoDB Configuration**: Update connection string if using external MongoDB

### Asset Files
Place required asset files in the appropriate directories:
```bash
# Create asset directories
mkdir -p agenda_generator/app/assets/doc
mkdir -p agenda_generator/app/assets/csv

# Place your PDF, Excel, and CSV files in these directories
```

## Running the Service

### With Docker (Recommended)
```bash
# For non-GPU servers
docker-compose up -d

# For GPU servers
docker-compose -f docker-compose.gpu.yml up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f agenda-service
docker-compose logs -f cache_server
```

### Manual Installation
```bash
# Start MongoDB and RabbitMQ (if not already running)
sudo systemctl start mongod rabbitmq-server

# Activate virtual environment
source agenda_env/bin/activate

# Start cache server (in one terminal)
cd climas_agenda_cacheserver/src
python main.py

# Start agenda service (in another terminal)
cd agenda_generator
python app/main.py
```

## Service Verification

### Check Service Health
```bash
# Test cache server API
curl http://localhost:8000/docs

# Check Docker container status
docker-compose ps

# Monitor logs
docker-compose logs -f
```

### Test GPU Support (if applicable)
```bash
# Check NVIDIA GPU availability
nvidia-smi

# Test CUDA in Python
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   ```bash
   sudo chown -R $USER:$USER /path/to/project
   ```

2. **Docker Permission Issues**
   ```bash
   sudo usermod -aG docker $USER
   newgrp docker
   ```

3. **GPU Not Detected**
   ```bash
   # Verify NVIDIA drivers
   nvidia-smi
   
   # Check Docker GPU support
   docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi
   ```

4. **Memory Issues**
   ```bash
   # Increase swap space
   sudo fallocate -l 4G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

5. **Port Conflicts**
   ```bash
   # Check port usage
   sudo netstat -tulpn | grep :8000
   sudo netstat -tulpn | grep :27017
   sudo netstat -tulpn | grep :5672
   ```

### Log Locations
- Docker logs: `docker-compose logs [service-name]`
- Manual installation logs: Check application output in terminal
- System logs: `/var/log/` directory

### Performance Optimization
- For GPU servers: Ensure CUDA memory is properly managed
- For high-load scenarios: Consider scaling with multiple container instances
- Monitor resource usage: `htop`, `nvidia-smi`, `docker stats`

## Security Considerations

1. **Firewall Configuration**
   ```bash
   # Allow only necessary ports
   sudo ufw allow 8000  # Cache server API
   sudo ufw enable
   ```

2. **Environment Variables**: Never commit `.env` files to version control

3. **API Keys**: Rotate API keys regularly and use environment-specific keys

4. **Database Security**: Configure MongoDB authentication for production

For additional support or issues, please refer to the project documentation or create an issue in the repository.
