
services:
 
  agenda-service:
    build:
      context: ./agenda_generator
      dockerfile: ./Dockerfile
    restart: always
    env_file:
      - ./agenda_generator/.env
    volumes:
      - ./agenda_generator/app:/app/app

  cache_server:

    build:
      context: ./climas_agenda_cacheserver
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8000:8000"
    volumes:
      - ./climas_agenda_cacheserver/src:/app
    env_file:
      - ./climas_agenda_cacheserver/.env
    
  