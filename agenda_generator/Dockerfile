# Use official Python image as base
FROM python:3.9-slim

WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    SPACY_WARNING_IGNORE=W008

RUN apt-get update && \
apt-get install -y supervisor && \
apt-get clean
# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libssl-dev \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*


# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install spacy==3.7.5 nltk==3.9.1

RUN python -m spacy download en_core_web_lg && \
    python -m spacy download en_core_web_md && \
    python -m nltk.downloader punkt  && \
    python -m nltk.downloader punkt_tab  && \
    python -m nltk.downloader vader_lexicon  && \
    python -m nltk.downloader stopwords




COPY requirements.txt .


COPY ./supervisor.conf /etc/supervisor/conf.d/

RUN pip install --no-cache-dir -r requirements.txt

RUN python -c "from transformers import GPT2LMHeadModel, GPT2Tokenizer; \
GPT2LMHeadModel.from_pretrained('gpt2-medium'); \
GPT2Tokenizer.from_pretrained('gpt2-medium')"

COPY . .

CMD ["supervisord", "-n", "-c", "/etc/supervisor/conf.d/supervisor.conf"]