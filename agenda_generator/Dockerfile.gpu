# Use NVIDIA CUDA base image for GPU support
FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

# Install Python 3.10 and pip
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3.10 \
    python3.10-dev \
    python3-pip \
    python3-setuptools \
    && ln -sf /usr/bin/python3.10 /usr/bin/python \
    && ln -sf /usr/bin/pip3 /usr/bin/pip

WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    SPACY_WARNING_IGNORE=W008

RUN apt-get update && \
apt-get install -y supervisor && \
apt-get clean
# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libssl-dev \
    ffmpeg \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Set CUDA related environment variables
ENV CUDA_HOME=/usr/local/cuda \
    PATH=/usr/local/cuda/bin:${PATH} \
    LD_LIBRARY_PATH=/usr/local/cuda/lib64:${LD_LIBRARY_PATH}


# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install spacy==3.7.5 nltk==3.9.1

RUN python -m spacy download en_core_web_lg && \
    python -m spacy download en_core_web_md && \
    python -m nltk.downloader punkt  && \
    python -m nltk.downloader punkt_tab  && \
    python -m nltk.downloader stopwords

RUN pip install --no-cache-dir \
    torch==2.0.1+cu118 \
    torchvision==0.15.2+cu118 \
    -f https://download.pytorch.org/whl/torch_stable.html

COPY requirements.txt .


COPY ./supervisor.conf /etc/supervisor/conf.d/

RUN pip install --no-cache-dir -r requirements.txt

RUN python -c "from transformers import GPT2LMHeadModel, GPT2Tokenizer; \
GPT2LMHeadModel.from_pretrained('gpt2-medium'); \
GPT2Tokenizer.from_pretrained('gpt2-medium')"


RUN pip install cupy-cuda11x

COPY . .

CMD ["supervisord", "-n", "-c", "/etc/supervisor/conf.d/supervisor.conf"]