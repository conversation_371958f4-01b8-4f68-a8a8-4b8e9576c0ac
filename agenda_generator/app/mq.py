
import os
import pika
def connect_to_rabbitmq():
    url = ""
    if os.getenv('MQ_URL'):
        url = os.getenv('MQ_URL')
    else:
        url = f"{os.getenv('MQ_PROTOCOL', 'amqp')}://{os.getenv('MQ_USERNAME', 'guest')}:{os.getenv('MQ_PASSWORD', 'guest')}@{os.getenv('MQ_HOST', 'localhost')}:5672/"
    
    print(f"Connecting to RabbitMQ at {url}")
    return pika.BlockingConnection(pika.URLParameters(url))