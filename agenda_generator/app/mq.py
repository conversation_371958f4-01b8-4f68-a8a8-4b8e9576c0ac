
import os
import pika

def connect_to_rabbitmq():
    url = ""
    if os.getenv('MQ_URL'):
        url = os.getenv('MQ_URL')
    else:
        url = f"{os.getenv('MQ_PROTOCOL', 'amqp')}://{os.getenv('MQ_USERNAME', 'guest')}:{os.getenv('MQ_PASSWORD', 'guest')}@{os.getenv('MQ_HOST', 'localhost')}:5672/"

    print(f"Connecting to RabbitMQ at {url}")

    # Configure connection parameters for long-running operations
    parameters = pika.URLParameters(url)

    # Set heartbeat to 0 to disable heartbeat timeout (for long-running operations)
    # Alternatively, you can set a longer heartbeat interval like 3600 (1 hour)
    parameters.heartbeat = 0

    # Set blocked connection timeout to handle flow control
    parameters.blocked_connection_timeout = 300

    # Set socket timeout
    parameters.socket_timeout = 300

    return pika.BlockingConnection(parameters)