import os
import requests
from typing import List 
from lib.processors.pdf_handler import PDFProcessor
from lib.processors.youtube_handler import YouTubeProcessor
from lib.processors.twitter_handler import TwitterProcessor
from lib.processors.news_handler import NewsProcessor
from lib.processors.keyword_processor import KeywordProcessor
from lib.processors.bluesky_handler import BlueskyProcessor


current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, os.pardir))

docx_file_path = f"{root_dir}/assets/doc/List of Dilemmas by ChatGPT.docx"
excel_file_path = f"{root_dir}/assets/doc/dilemma.xlsx"
csv_file_path = f"{root_dir}/assets/csv/"
def get_keywords_from_pdf(pdf, target_country, years_to_process, default_climate_keywords):
    pdf_processor = PDFProcessor(pdf.get('attachments'), default_climate_keywords)
    
    pdf_processor.process_pdfs()
    pdf_final_keywords = []
    grouped_texts_by_year = {} 
    if target_country == "all":
        pdf_final_keywords = pdf_processor.process_and_extract_keywords(years_to_process)
        grouped_texts_by_year = pdf_processor.get_grouped_texts_by_year()
    else:
        for year in years_to_process:
            if (target_country, year) in pdf_processor.grouped_texts_by_country_year:
                texts = pdf_processor.grouped_texts_by_country_year[(target_country, year)]
                combined_text = ' '.join(texts)
                grouped_texts_by_year[year] = combined_text
                
                keyword_processor = KeywordProcessor(default_climate_keywords)
                extracted_keywords = keyword_processor.extract_keywords(combined_text)
                lda_keywords = keyword_processor.perform_lda_topic_modeling(texts)
                filtered_keywords = keyword_processor.filter_keywords_by_spacy_similarity(lda_keywords, extracted_keywords)
                year_keywords = keyword_processor.filter_keywords_by_climate_similarity(filtered_keywords)
                pdf_final_keywords.extend(year_keywords)
    return pdf_final_keywords, grouped_texts_by_year




def process_agenda_sources(agenda_sources):
    agenda_by_type = {
        'social_media': None,
        'printed_media': None,
        'pdf': None
    }

    for agenda in agenda_sources:
        agenda_type = agenda.get('type')
        if agenda_type in agenda_by_type and agenda_by_type[agenda_type] is None:
            print ( agenda)
            agenda_by_type[agenda_type] = agenda

    return agenda_by_type['social_media'], agenda_by_type['printed_media'], agenda_by_type['pdf']



def merge_keywords(pdf_keywords, youtube_keywords, news_keywords, twitter_keywords, bluesky_keywords ):
    final_keywords = set(pdf_keywords)
    final_keywords.update(*youtube_keywords)  # Unpacks the lists within youtube_keywords
    final_keywords.update(news_keywords, twitter_keywords)
    final_keywords.update(bluesky_keywords)
    return list(final_keywords)




def get_keywords_from_youtube (comments, default_climate_keywords ): 
    try:
        youtube_processor = YouTubeProcessor( climate_keywords=default_climate_keywords)

        print("process_comments")
        
        youtube_processor.process_youtube_comments(comments)
        youtube_keywords_results = youtube_processor.get_results()
        print("youtube keyword", youtube_keywords_results)
        return youtube_keywords_results, comments
    except KeyError as e:  # Handling 'videoId' missing
        print(f"KeyError: Missing key {e} in YouTube response")
    except Exception as e:  # Catch other unexpected issues
        print(f"Error processing YouTube data: {e}")
    
    return []



def get_keywords_from_twitter (tweets, default_climate_keywords): 
    try:
        
        
        twitter_processor = TwitterProcessor(  climate_keywords=default_climate_keywords)
       
        print("process_tweets")
        twitter_keywords_results = twitter_processor.process_tweets(tweets)
        print("Twitter keyword", twitter_keywords_results)
        return twitter_keywords_results, tweets
    except KeyError as e:  
        print(f"KeyError: Missing key {e} in Twitter response")
    except Exception as e: 
        print(f"Error processing Twitter data: {e}")
    
    return []

def get_keywords_from_bluesky (posts, default_climate_keywords): 
    try:
        
        
        bluesky_processor = BlueskyProcessor(  climate_keywords=default_climate_keywords)   
       
        print("process_Blusky")
        bluesky_keywords_results = bluesky_processor.process_posts(posts)
        print("Blusky keyword", bluesky_keywords_results)
        return bluesky_keywords_results, posts
    except KeyError as e:  
        print(f"KeyError: Missing key {e} in Twitter response")
    except Exception as e: 
        print(f"Error processing Twitter data: {e}")
    
    return []


def get_keywords_from_news (articles, default_climate_keywords): 
    try:

       
        news_processor = NewsProcessor( climate_keywords=default_climate_keywords)
        
        print("process_articles")
        news_keywords_results = news_processor.process_articles(articles)
        print("News keyword", news_keywords_results)
        return news_keywords_results, articles
    except KeyError as e:  
        print(f"KeyError: Missing key {e} in News response")
    except Exception as e: 
        print(f"Error processing News data: {e}")
    
    return []




def fetch_all_data(keywords: List[str], years: List[int], sources: List[str], source_uri: List[str]):
    """
    Centralized data fetching function that gets data for all sources at once
    """
    
    cache_db_url = os.getenv('CACHE_DB_URL')
        
    url = f"{cache_db_url}/api/v1/search"
    headers = {"Content-Type": "application/json"}
    params = {
        "keywords": keywords,
        "sources": sources,
        "source_uri": [] if source_uri is None else source_uri,
        "years": years
    }
    
    try:
        response = requests.post(url, headers=headers, json=params)
        response.raise_for_status()
        data = response.json()
        return data.get("results", {})

    except Exception as e:
        print(f"⚠️ Error fetching data: {e}")
        return {}

def get_year_range( start_year, end_year):
        """Generate a list of years between start_year and end_year inclusive"""
        return list(range(start_year, end_year + 1))