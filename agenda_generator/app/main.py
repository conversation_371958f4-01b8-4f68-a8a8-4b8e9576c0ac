
import os
import pika
import time
import json
import threading
from dotenv import load_dotenv
from mq import connect_to_rabbitmq
from event_enum import event_dict
from data_proccessor import process_data
from generate_agenda import generate_agenda
from pdf_reference import get_pdf_reference
from lib.analyzer.frequency_analyzer import analyze_keywords
from lib.analyzer.youtube_sentiment_analyzer import analyze_sentiment
load_dotenv()

# Global connection and channel for acknowledgments
current_connection = None
current_channel = None
connection_lock = threading.Lock()
heartbeat_timer = None

def send_heartbeat():
    """Send periodic heartbeats to keep connection alive during long operations"""
    global current_connection, heartbeat_timer

    try:
        with connection_lock:
            if current_connection and not current_connection.is_closed:
                current_connection.process_data_events(time_limit=0)
                # Schedule next heartbeat
                heartbeat_timer = threading.Timer(30.0, send_heartbeat)
                heartbeat_timer.start()
    except Exception as e:
        print(f"Error sending heartbeat: {e}")

def start_heartbeat():
    """Start the heartbeat timer for long-running operations"""
    global heartbeat_timer

    if heartbeat_timer:
        heartbeat_timer.cancel()

    heartbeat_timer = threading.Timer(30.0, send_heartbeat)
    heartbeat_timer.start()

def stop_heartbeat():
    """Stop the heartbeat timer"""
    global heartbeat_timer

    if heartbeat_timer:
        heartbeat_timer.cancel()
        heartbeat_timer = None

def ensure_connection():
    """Ensure we have a valid connection and channel for acknowledgments"""
    global current_connection, current_channel

    with connection_lock:
        if current_connection is None or current_connection.is_closed:
            current_connection = connect_to_rabbitmq()
            current_channel = current_connection.channel()
        elif current_channel is None or current_channel.is_closed:
            current_channel = current_connection.channel()

    return current_channel

def safe_ack(delivery_tag, original_channel):
    """Safely acknowledge a message, handling connection issues"""
    try:
        # First try with the original channel
        if original_channel and not original_channel.is_closed:
            original_channel.basic_ack(delivery_tag=delivery_tag)
            print(f"Message acknowledged successfully with original channel")
            return True
    except Exception as e:
        print(f"Failed to ack with original channel: {e}")

    try:
        # If original channel fails, try with a fresh connection
        ack_channel = ensure_connection()
        ack_channel.basic_ack(delivery_tag=delivery_tag)
        print(f"Message acknowledged successfully with fresh channel")
        return True
    except Exception as e:
        print(f"Failed to acknowledge message even with fresh connection: {e}")
        return False

def callback(ch, method, properties, body):
    params = json.loads(body.decode('utf-8'))
    event = params['event']

    # TODO: will fix this
    check_project_in_events = [event_dict['PROCESS_DILEMMA'], event_dict['ANALYZE_KEYWORD_FREQUENCY'], event_dict['ANALYZE_SENTIMENT']]
    project_id = params['project'].get('id') if event in check_project_in_events else params['project_id']
    print(f"Received event: {event}")

    # Start heartbeat for long-running operations
    start_heartbeat()

    processing_successful = False
    try:
        if event == event_dict['PROCESS_DILEMMA']:
            process_data(params)
        elif event == event_dict['PROCESS_AGENDA']:
            generate_agenda(params)
        elif event == event_dict['GET_PDF_REFERENCE']:
            get_pdf_reference(params)
        elif event == event_dict['ANALYZE_KEYWORD_FREQUENCY']:
            analyze_keywords(params)
        elif event == event_dict['ANALYZE_SENTIMENT']:
            analyze_sentiment(params)

        processing_successful = True
        print(f"Successfully processed event: {event}")

    except Exception as e:
        error_message = str(e)
        print(f"Error processing message: {error_message}")
        # Still acknowledge the message to prevent reprocessing
        processing_successful = True
    finally:
        # Stop heartbeat after processing
        stop_heartbeat()

    # Always try to acknowledge the message
    if processing_successful:
        ack_success = safe_ack(method.delivery_tag, ch)
        if not ack_success:
            print(f"WARNING: Failed to acknowledge message for event {event}. Message may be redelivered.")
    else:
        print(f"Skipping acknowledgment due to processing failure for event {event}")



def start_consuming():
    global current_connection, current_channel

    while True:
        try:
            connection = connect_to_rabbitmq()
            channel = connection.channel()

            # Update global connection references
            with connection_lock:
                current_connection = connection
                current_channel = channel

            queue_name = os.getenv("MQ_ROUTE_KEY")
            channel.queue_declare(queue=queue_name, durable=True)

            # Set QoS to process one message at a time to prevent overwhelming
            channel.basic_qos(prefetch_count=1)

            channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=False)

            print("RabbitMQ consuming started. Waiting for messages...")
            channel.start_consuming()

        except pika.exceptions.StreamLostError as e:
            print(f"Stream connection lost: {e}. Reconnecting...")
        except pika.exceptions.AMQPConnectionError as e:
            print(f"AMQP connection error: {e}. Reconnecting...")
        except KeyboardInterrupt:
            print("Received interrupt signal. Shutting down gracefully...")
            break
        except Exception as e:
            print(f"Unexpected error: {e}. Reconnecting...")
        finally:
            # Stop any running heartbeat
            stop_heartbeat()

            # Clean up global references
            with connection_lock:
                current_channel = None
                current_connection = None

            # Close the connection if it exists
            if 'connection' in locals() and connection and connection.is_open:
                try:
                    connection.close()
                except Exception as e:
                    print(f"Error closing connection: {e}")
            # Wait before reconnecting
            time.sleep(5)


if __name__ == '__main__':
    start_consuming()
    

# if __name__ == '__main__':
#     url = ""

#     if os.getenv('MQ_URL'): 
#         url = os.getenv('MQ_URL')
        
#     else: 
#         url = f"{os.getenv('MQ_PROTOCOL', 'amqp')}://{os.getenv('MQ_USERNAME', 'guest')}:{os.getenv('MQ_PASSWORD', 'guest')}@{os.getenv('MQ_HOST', 'localhost')}:5672/"
   
#     print(url)
#     connection = pika.BlockingConnection(pika.URLParameters(url))
#     channel = connection.channel()
    
#     # receive message and complete the task
#     print("--------s----------------------------------- RabbitMQ Consuming Started -------------------------------------------")
    
#     channel.queue_declare(queue=os.getenv("MQ_ROUTE_KEY"), durable=True)
#     channel.basic_consume(os.getenv("MQ_ROUTE_KEY"), callback, auto_ack=False)
#     channel.start_consuming()