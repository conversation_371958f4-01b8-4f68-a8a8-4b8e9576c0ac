
import os
import pika
import time
import json
from dotenv import load_dotenv
from mq import connect_to_rabbitmq
from event_enum import event_dict
from data_proccessor import process_data
from generate_agenda import generate_agenda
from pdf_reference import get_pdf_reference
from lib.analyzer.frequency_analyzer import analyze_keywords
from lib.analyzer.youtube_sentiment_analyzer import analyze_sentiment
load_dotenv()

def callback(ch, method, properties, body):
    params = json.loads(body.decode('utf-8'))
    event = params['event']
    
    # TODO: will fix this 
    check_project_in_events = [event_dict['PROCESS_DILEMMA'], event_dict['ANALYZE_KEYWORD_FREQUENCY'], event_dict['ANALYZE_SENTIMENT']]
    project_id = params['project'].get('id') if event in check_project_in_events else params['project_id']
    print(f"Received event: {event}")
    try:
        if event == event_dict['PROCESS_DILEMMA']:
            process_data(params)
        elif event == event_dict['PROCESS_AGENDA']:
            generate_agenda(params)
        elif event == event_dict['GET_PDF_REFERENCE']:
            get_pdf_reference(params)
        elif event == event_dict['ANALYZE_KEYWORD_FREQUENCY']:
            analyze_keywords(params)
        elif event == event_dict['ANALYZE_SENTIMENT']:
            analyze_sentiment(params)
            
    except Exception as e:
        error_message = str(e)
        print(f"Error processing message: {error_message}")
        



def start_consuming():
    while True:
        try:
            connection = connect_to_rabbitmq()
            channel = connection.channel()

            queue_name = os.getenv("MQ_ROUTE_KEY")
            channel.queue_declare(queue=queue_name, durable=True)
            channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=True)

            print("RabbitMQ consuming started. Waiting for messages...")
            channel.start_consuming()
        except pika.exceptions.StreamLostError as e:
            print(f"Stream connection lost: {e}. Reconnecting...")
        except pika.exceptions.AMQPConnectionError as e:
            print(f"AMQP connection error: {e}. Reconnecting...")
        except Exception as e:
            print(f"Unexpected error: {e}. Reconnecting...")
        finally:
            # Close the connection if it exists
            if 'connection' in locals() and connection.is_open:
                connection.close()
            # Wait before reconnecting
            time.sleep(5)


if __name__ == '__main__':
    start_consuming()
    

# if __name__ == '__main__':
#     url = ""

#     if os.getenv('MQ_URL'): 
#         url = os.getenv('MQ_URL')
        
#     else: 
#         url = f"{os.getenv('MQ_PROTOCOL', 'amqp')}://{os.getenv('MQ_USERNAME', 'guest')}:{os.getenv('MQ_PASSWORD', 'guest')}@{os.getenv('MQ_HOST', 'localhost')}:5672/"
   
#     print(url)
#     connection = pika.BlockingConnection(pika.URLParameters(url))
#     channel = connection.channel()
    
#     # receive message and complete the task
#     print("--------s----------------------------------- RabbitMQ Consuming Started -------------------------------------------")
    
#     channel.queue_declare(queue=os.getenv("MQ_ROUTE_KEY"), durable=True)
#     channel.basic_consume(os.getenv("MQ_ROUTE_KEY"), callback, auto_ack=False)
#     channel.start_consuming()