import pandas as pd
import math
from lib.analyzer.frequency_analyzer import FrequencyAnalyzer

class OntologyAnalyzer:
    def __init__(self, keywords):
        self.df = pd.DataFrame(keywords)

    def extract_observations(self):
        df = self.df
        df['Topic'] = df['keyword']
        df['Year'] = df['year'].astype(int)
        df['Freq'] = df['frequency'].astype(int)
        df['ObsID'] = [f"obs{i}" for i in range(1, len(df)+1)]
        return df

    def analyze_trends(self, df):
        output_records = []
        grouped = df.groupby("Topic")

        for topic, group in grouped:
            grp_sorted = group.sort_values("Year")
            years = grp_sorted["Year"].tolist()
            freqs = grp_sorted["Freq"].tolist()
            obs_ids = grp_sorted["ObsID"].tolist()
            N = len(years)

            increment_count = sum(1 for i in range(1, N) if freqs[i] > freqs[i-1])
            threshold = math.floor(N / 2)
            qualifies = increment_count >= threshold

            output_records.append({
                "Topic": topic,
                "N_years": N,
                "Years": ", ".join(map(str, years)),
                "Freqs": ", ".join(map(str, freqs)),
                "ObsIDs": ", ".join(obs_ids),
                "IncrementCount": increment_count,
                "Threshold": threshold,
                "Qualifies": "Yes" if qualifies else "No"
            })

        return pd.DataFrame(output_records)
    def get_qualified_keywords(self):
        df = self.extract_observations()
        trend_df = self.analyze_trends(df)
        qualified = []
        qualified = trend_df[trend_df["Qualifies"] == "Yes"]["Topic"].tolist()
        return qualified



def analyze_with_ontology(pdf_data, keywords_array, target_start_year, target_end_year):
        print("analyze_with_ontology")
        frequency_analyzer = FrequencyAnalyzer()
        frequency_analyzer.add_data_source('pdf', pdf_data)
        frequency_data = frequency_analyzer.get_frequency_data(keywords_array, target_start_year, target_end_year)
        
        analyzer = OntologyAnalyzer(frequency_data)
        pdf_final_keywords = analyzer.get_qualified_keywords()
        return pdf_final_keywords