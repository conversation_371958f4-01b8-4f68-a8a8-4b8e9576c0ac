import os
import io
import base64
import pandas as pd
import numpy as np
from collections import defaultdict
import difflib
import matplotlib.pyplot as plt
import re
from lib.processors.pdf_handler import PDFProcessor
from utils.utils import fetch_all_data, process_agenda_sources, get_year_range
from lib.default_keywords import default_climate_keywords
from event_enum import event_dict
from mq import connect_to_rabbitmq
import json




    
    
    
class FrequencyAnalyzer:
    def __init__(self):
        self.df = pd.DataFrame(columns=['text', 'year'])
        self.grouped_texts_by_year = defaultdict(str)
        self.phrases_to_analyze = default_climate_keywords
        self.all_data = {}

    def preprocess_text(self, text):
        """Preprocess text by lowercasing, removing punctuation and extra whitespace"""
        text = text.lower()
        text = re.sub(r'[^\w\s]', '', text)  # Remove punctuation
        text = re.sub(r'\s+', ' ', text)     # Remove extra whitespace
        return text.strip()

    

    def process_youtube_data(self, youtube_data):
        """Process YouTube data and add to DataFrame"""
        data = []
        for item in youtube_data:
            year_str = item.get('published_at', '').split('-')[0] if item.get('published_at') else None
            year = int(year_str) if year_str and year_str.isdigit() else None
            data.append({
                'text': item.get('comment', ''),
                'year': year,
               
            })
        return pd.DataFrame(data)

    def process_twitter_data(self, twitter_data):
        """Process Twitter data and add to DataFrame"""
        data = []
        for item in twitter_data:
            year_str = item.get('published_at', '').split('-')[0] if item.get('published_at') else None
            year = int(year_str) if year_str and year_str.isdigit() else None
            data.append({
                'text': item.get('text', ''),
                'year': year,
               
            })
        return pd.DataFrame(data)

    def process_bluesky_data(self, bluesky_data):
        """Process Bluesky data and add to DataFrame"""
        data = []
        for item in bluesky_data:
            year_str = item.get('published_at', '').split('-')[0] if item.get('published_at') else None
            year = int(year_str) if year_str and year_str.isdigit() else None
            data.append({
                'text': item.get('text', ''),
                'year': year,
               
            })
        return pd.DataFrame(data)

    def process_printed_media_data(self, printed_media_data):
        """Process printed media data and add to DataFrame"""
        data = []
        for item in printed_media_data:
            text = f"{item.get('title', '')} {item.get('description', '')}".strip()
            year_str = item.get('published_at', '').split('-')[0] if item.get('published_at') else None
            year = int(year_str) if year_str and year_str.isdigit() else None
            data.append({
                'text': text,
                'year': year,
               
            })
        return pd.DataFrame(data)

    def process_pdf_data(self, pdf_data):
        """Process PDF data and add to DataFrame"""
        data = []
        for year, texts in pdf_data.items():
            if isinstance(texts, list):
                for text in texts:
                    data.append({
                        'text': text,
                        'year': int(year) if isinstance(year, (int, str)) and str(year).isdigit() else None,
                       
                    })
            else:
                data.append({
                    'text': texts,
                    'year': int(year) if isinstance(year, (int, str)) and str(year).isdigit() else None,
                    
                })
        
        return pd.DataFrame(data)

    def add_data_source(self, source_name, data):
        """Add data from a specific source to the main DataFrame"""
        processor = getattr(self, f'process_{source_name}_data', None)
        if processor:
            df = processor(data)
            self.df = pd.concat([self.df, df], ignore_index=True)
        else:
            raise ValueError(f"No processor available for source: {source_name}")

    def group_texts_by_year(self):
        """Combine all texts by year for analysis"""
        for year, group in self.df.groupby('year'):
            combined_text = ' '.join(group['text'].dropna().astype(str))
            self.grouped_texts_by_year[year] = self.preprocess_text(combined_text)

    def find_closest_phrase(self, user_input):
        """Find the closest matching phrase from predefined keywords"""
        processed_input = self.preprocess_text(user_input)
        match = difflib.get_close_matches(processed_input, self.phrases_to_analyze, n=1, cutoff=0.4)
        return match[0] if match else None

    def count_phrase_occurrences(self, text, phrase):
        """Count occurrences of a phrase in text"""
        words = text.split()
        phrase_words = phrase.split()
        phrase_length = len(phrase_words)
        count = 0
        for i in range(len(words) - phrase_length + 1):
            if words[i:i + phrase_length] == phrase_words:
                count += 1
        return count

    def get_frequency_data(self, keywords, target_start_year, target_end_year):
        """Calculate frequency data for keywords across years"""
        target_years = get_year_range(target_start_year, target_end_year)
        results = []
        
        for keyword in keywords:  
            matched_phrase = self.find_closest_phrase(keyword)
            if not matched_phrase:
                print(f"No close match found for the input phrase: {keyword}")
                continue
                
            processed_phrase = self.preprocess_text(matched_phrase)
            
            for year in target_years:
                text = self.grouped_texts_by_year.get(year, "")
                count = self.count_phrase_occurrences(text, processed_phrase)
                
                results.append({
                    "keyword": keyword,
                    "matched_phrase": matched_phrase,
                    "year": year,
                    "frequency": count
                })
        
        return results

    def generate_plots(self, frequency_data):
        """Generate plots from frequency data"""
        base64_png = []
        
        # Group data by keyword
        df = pd.DataFrame(frequency_data)
        grouped = df.groupby('keyword')
        
        for keyword, group in grouped:
            years = group['year'].tolist()
            frequencies = group['frequency'].tolist()
            matched_phrase = group['matched_phrase'].iloc[0]
            
            plt.figure(figsize=(6, 4))
            plt.bar([str(y) for y in years], frequencies, color=['#2a9d8f'] * len(years))
            plt.title(f"Frequency of '{matched_phrase}' by Year")
            plt.xlabel("Year")
            plt.ylabel("Frequency")
            plt.grid(axis='y', linestyle='--', alpha=0.6)
            plt.tight_layout()
            plt.show()
            buf = io.BytesIO()
            plt.savefig(buf, format='png', bbox_inches='tight')
            buf.seek(0)
            png_data = base64.b64encode(buf.getvalue()).decode('utf-8')
            buf.close()
            base64_png.append(png_data)
            
            plt.close()
            
        return base64_png
    def generate_combined_plot(self, frequency_data):
        """Generate a single plot showing all keywords' frequencies with clear year separation"""
        if not frequency_data:
            return None
            
        # Convert to DataFrame
        df = pd.DataFrame(frequency_data)
        
        # Get unique years and keywords
        years = sorted(df['year'].unique())
        keywords = df['keyword'].unique()
        
        # Setup figure (3:1 landscape ratio)
        plt.figure(figsize=(18, 6), dpi=100, facecolor='white')
        ax = plt.gca()
        
        # Style configuration
        BAR_WIDTH = 0.8 / len(keywords)  # Dynamic width based on keyword count
        COLORS = plt.cm.plasma(np.linspace(0, 1, len(keywords)))  # Color gradient
        
        # Plot each keyword's data
        for i, keyword in enumerate(keywords):
            keyword_data = df[df['keyword'] == keyword]
            x_pos = [years.index(year) + (i * BAR_WIDTH) for year in keyword_data['year']]
            ax.bar(x_pos, keyword_data['frequency'], 
                width=BAR_WIDTH,
                color=COLORS[i],
                label=keyword,
                edgecolor='white',
                linewidth=0.5,
                zorder=2)
        
        # Enhanced Year Separators
        separator_style = {
            'color': '#333333',
            'linestyle': '--',
            'linewidth': 1.2,
            'alpha': 0.5,
            'zorder': 1  # Place behind bars but above grid
        }
        
        # Add vertical separators between years
        for year_idx in range(len(years)+1):
            sep_pos = year_idx - 0.5
            ax.axvline(x=sep_pos, **separator_style)
        
        # X-axis configuration
        ax.set_xticks(np.arange(len(years)))
        ax.set_xticklabels(years)
        ax.set_xlim(-0.5, len(years)-0.5)  # Add padding
        
        # Styling
        ax.set_title('Keyword Frequency Analysis by Year', 
                fontsize=16, pad=20)
        ax.set_xlabel('Year', fontsize=12, labelpad=10)
        ax.set_ylabel('Frequency', fontsize=12, labelpad=10)
        
        # Custom grid (horizontal only)
        ax.grid(axis='y', color='lightgray', linestyle=':', alpha=0.7)
        
        # Legend with transparency
        ax.legend(title='Keywords', 
                bbox_to_anchor=(1.02, 1), 
                loc='upper left',
                framealpha=0.9)
        
        plt.tight_layout()
        
        # Save to base64
        buf = io.BytesIO()
        plt.savefig(buf, format='png', bbox_inches='tight', dpi=100)
        buf.seek(0)
        png_data = base64.b64encode(buf.getvalue()).decode('utf-8')
        buf.close()
        plt.close()
        
        return [png_data]





def data_source_manager( agenda_sources, target_start_year,  target_end_year, keywords):
    social_media, printed_media, pdf = process_agenda_sources(agenda_sources)
    
    years_to_process = get_year_range(target_start_year, target_end_year)
    
    process_youtube = social_media and any('youtube' in url.lower() for url in social_media.get('urls', []))
    process_twitter = social_media and any('twitter' in url.lower() for url in social_media.get('urls', []))
    process_bluesky = social_media and any('bluesky' in url.lower() for url in social_media.get('urls', []))
    process_printed_media = len(printed_media.get('urls', [])) > 0


    all_data = {}
    
    sources = []
    if process_youtube:
        sources.append("youtube")
    if process_twitter:
        sources.append("twitter")
    if process_bluesky:
        sources.append("bluesky")
    if process_printed_media:
        sources.append("printed_media")
    
    # Fetch all data at once
    print(sources)
    print(years_to_process)
    print(keywords)
    print(process_printed_media)
    if sources:
        all_data = fetch_all_data(
            keywords=keywords,
            years=years_to_process,
            sources=sources,
            source_uri=printed_media.get('urls') if process_printed_media else None
        )

    pdf_data = None
    if pdf and pdf.get('attachments'):
        pdf_processor = PDFProcessor(pdf.get('attachments'), default_climate_keywords)
        pdf_processor.process_pdfs()
        pdf_data =  pdf_processor.get_grouped_texts_by_year()

    return all_data, pdf_data

def analyze_keywords(body):
    queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
    try: 
        project = body.get("project", {})
        agenda_sources = body.get("agenda_sources", [])
        target_start_year = int(project.get("start_year"))
        target_end_year = int(project.get("end_year"))
        keywords = project.get("keywords")
        keywords_array =  [item.strip() for item in keywords.split(",")] if isinstance(keywords, str) else []
    
        analyzer = FrequencyAnalyzer()
        all_data, pdf_data = data_source_manager(agenda_sources, target_start_year,  target_end_year, keywords_array)
        
        
        if 'youtube' in all_data:
                analyzer.add_data_source('youtube', all_data['youtube'])
        if 'twitter' in all_data:
                analyzer.add_data_source('twitter', all_data['twitter'])
        if 'bluesky' in all_data:
                analyzer.add_data_source('bluesky', all_data['bluesky'])
        if 'printed_media' in all_data:
                analyzer.add_data_source('printed_media', all_data['printed_media'])
        if pdf_data:
                analyzer.add_data_source('pdf', pdf_data)
        
        analyzer.group_texts_by_year()
        
        frequency_data = analyzer.get_frequency_data(keywords_array, target_start_year, target_end_year)
        analyze_keywords_pngs = analyzer.generate_plots(frequency_data)
        
        
        payload = json.dumps({
                "event": event_dict['KEYWORD_FREQUENCY_RESULT'],
                "body": {
                    "project_id": project.get("id"),
                    "result": {
                        "type": "frequency_analysis",
                        "data": analyze_keywords_pngs
                        },
                }
                })
            
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
    except Exception as e:
        print("Error in analyze_keywords", e)
        
        payload = json.dumps({
            "event": event_dict['FAILED_RESULT'],
            "body": {
                "type": "frequency_analysis",
                "status": "failed",
                "project_id": project.get("id"),
                "message": "Failed to analyze keyword frequency",
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)
        connection.close()
        
    
    
    


    