import os
import pandas as pd
import plotly.graph_objects as go
from nltk.sentiment import SentimentIntensityAnalyzer
from utils.utils import fetch_all_data
from event_enum import event_dict
from mq import connect_to_rabbitmq
import json

class YouTubeSentimentAnalyzer:
    def __init__(self, comments_data, start_date, end_date):
        
        
        self.start_date = start_date
        self.end_date = end_date
        self.comments_data = comments_data

    def analyze_sentiment(self, ):
        print("Analyzing sentiment...")
        df = self.comments_data
        
        df = pd.DataFrame(self.comments_data)
        df['date'] = pd.to_datetime(df['published_at'])
        sia = SentimentIntensityAnalyzer()

        # Sentiment scores for all comments
        df['sentiment'] = df['comment'].astype(str).apply(lambda x: sia.polarity_scores(x)['compound'])

        # Group all comments by year-month
        df['year_month'] = df['date'].dt.to_period('M')
        df_avg = df.groupby('year_month').agg({'sentiment': 'mean'}).reset_index()

        # Smoothing and normalization
        df_avg['smoothed_sentiment'] = df_avg['sentiment'].rolling(window=3, min_periods=1).mean()
        max_sentiment = df_avg['smoothed_sentiment'].max()
        df_avg['percentage_sentiment'] = (df_avg['smoothed_sentiment'] / max_sentiment) * 100

        return self._plot_sentiment(df_avg)

    def _plot_sentiment(self, df_avg):
        print("Plotting sentiment...")
        mean_sentiment = df_avg['smoothed_sentiment'].mean()
        line_color = 'green' if mean_sentiment > 0 else 'red'
        gradient_color = 'rgba(0,255,0,0.1)' if mean_sentiment > 0 else 'rgba(255,0,0,0.1)'

        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=df_avg['year_month'].astype(str),
            y=df_avg['percentage_sentiment'],
            mode='lines',
            line=dict(color=line_color),
            name='Sentiment %'
        ))
        fig.add_annotation(
            x=0.5, y=-0.36, xref="paper", yref="paper",
            text=f'Mean Sentiment Score: {mean_sentiment:+.2f}',
            showarrow=False,
            font=dict(size=12)
        )
        fig.update_layout(
            shapes=[
                dict(type="rect", x0=0, x1=1, y0=0, y1=0.1,
                     xref="paper", yref="paper",
                     fillcolor=gradient_color, opacity=0.3, layer="below")
            ],
            title="Smoothed Sentiment Trend Over Time (All Comments)",
            xaxis_title="Month",
            yaxis_title="Sentiment %",
            xaxis=dict(tickangle=45),
            showlegend=False,
            plot_bgcolor='white',
            margin=dict(t=50, b=120, l=50, r=50)
        )
        
        fig_data = fig.to_json()
        
        return fig_data

    def run(self):
        return self.analyze_sentiment()



def get_year_range(start_year, end_year):
        """Generate a list of years between start_year and end_year inclusive."""
        return list(range(start_year, end_year + 1))
    
    
def analyze_sentiment(body):
    queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
    try:
        project = body.get("project", {})
        
        target_start_year = int(project.get("start_year"))
        target_end_year = int(project.get("end_year"))
        keywords = project.get("keywords")
        keywords_array =  [item.strip() for item in keywords.split(",")] if isinstance(keywords, str) else []
    
    
    
        years_to_process = get_year_range(target_start_year, target_end_year)
        
        
        all_data = fetch_all_data(
                    keywords=keywords_array,
                    years=years_to_process,
                    sources=["youtube"],
                    source_uri=None
                )
        youtube_comments = all_data.get("youtube", [])
        
        
        analyzer = YouTubeSentimentAnalyzer( youtube_comments, target_start_year, target_end_year)
        sentiment_json = analyzer.run()
    
        payload = json.dumps({
                "event": event_dict['SENTIMENT_ANALYSIS_RESULT'],
                "body": {
                    "project_id": project.get("id"),
                    "result": {
                        "type": "sentiment_analysis",
                        "data": sentiment_json
                        },
                }
                })
            
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
    
    except Exception as e:
        print("Error in analyze_sentiment", e)
        
        payload = json.dumps({
            "event": event_dict['FAILED_RESULT'],
            "body": {
                "type": "sentiment_analysis",
                "status": "failed",
                "project_id": project.get("id"),
                "message": "Failed to analyze sentiment",
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()

    