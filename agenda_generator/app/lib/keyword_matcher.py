from sklearn.metrics.pairwise import cosine_similarity
import pandas as pd
import spacy
import re
import numpy as np
import torch
from lib.processors.keyword_processor import KeywordProcessor, DEVICE
from concurrent.futures import ThreadPoolExecutor

# Load the NLP model once to avoid loading it repeatedly
nlp = spacy.load('en_core_web_md')

# Enable GPU if available for spaCy
try:
    if torch.cuda.is_available():
        spacy.require_gpu()
        print("GPU acceleration enabled for spaCy")
except Exception as e:
    print(f"GPU acceleration not available for spaCy: {e}")
    print("Falling back to CPU processing for spaCy")

def preprocess_text(text):
        """
        Preprocess text: lowercasing and removing punctuation.
        """
        return re.sub(r'[^\w\s]', '', text.lower())




def batch_vectorize(texts, nlp, batch_size=1000):
    """Vectorize texts in batches using optimized GPU acceleration."""
    if not texts:
        return np.array([], dtype=np.float32)
    
    # Pre-allocate list with known size for better memory efficiency
    total_batches = (len(texts) + batch_size - 1) // batch_size
    vectors = [None] * total_batches
    
    def process_batch(batch_idx):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(texts))
        batch = texts[start_idx:end_idx]
        
        # Process texts in parallel and keep on GPU
        docs = list(nlp.pipe(batch, batch_size=min(32, len(batch))))
        batch_vectors = torch.stack([torch.tensor(doc.vector, dtype=torch.float32, device=DEVICE) for doc in docs])
        
        # Normalize vectors on GPU for better similarity computation later
        batch_vectors = torch.nn.functional.normalize(batch_vectors, p=2, dim=1)
        return batch_idx, batch_vectors
    
    # Process batches in parallel
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(process_batch, i) for i in range(total_batches)]
        
        # Collect results in order
        for future in futures:
            idx, batch_tensor = future.result()
            vectors[idx] = batch_tensor.cpu().numpy()
    
    # Concatenate all batches efficiently
    return np.vstack(vectors)

def match_keywords_with_dilemmas(dilemmas_df, keywords, comments, articles, bluesky_posts, threshold=0.72):
    """Highly optimized version using parallel processing and efficient tensor operations."""
    # Process all text data in parallel using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=4) as executor:
        # Preprocess all texts in parallel
        future_keywords = executor.submit(lambda: [preprocess_text(k) for k in keywords])
        future_a_texts = executor.submit(lambda: [preprocess_text(row["column_a"]) for _, row in dilemmas_df.iterrows()])
        future_b_texts = executor.submit(lambda: [preprocess_text(row["column_b"]) for _, row in dilemmas_df.iterrows()])
        future_titles = executor.submit(lambda: dilemmas_df["title"].tolist())
        
        # Get preprocessed texts
        preprocessed_keywords = future_keywords.result()
        a_texts = future_a_texts.result()
        b_texts = future_b_texts.result()
        dilemma_titles = future_titles.result()
        
        # Vectorize all texts in parallel
        future_keyword_vectors = executor.submit(lambda: batch_vectorize(preprocessed_keywords, nlp))
        future_a_vectors = executor.submit(lambda: batch_vectorize(a_texts, nlp))
        future_b_vectors = executor.submit(lambda: batch_vectorize(b_texts, nlp))
        future_title_vectors = executor.submit(lambda: batch_vectorize(dilemma_titles, nlp))
        
        # Process articles and comments in parallel if they exist
        if articles:
            future_article_data = executor.submit(lambda: (
                [preprocess_text(article.get("title", "")) for article in articles],
                [article.get("url", "") for article in articles]
            ))
            article_titles, article_urls = future_article_data.result()
            future_article_vectors = executor.submit(lambda: batch_vectorize(article_titles, nlp))
        else:
            article_vectors = np.array([])
            article_urls = []
        
        if comments:
            future_comment_data = executor.submit(lambda: (
                [preprocess_text(comment["comment"]) for comment in comments],
                [comment["video_url"] for comment in comments]
            ))
            comment_texts, comment_urls = future_comment_data.result()
            future_comment_vectors = executor.submit(lambda: batch_vectorize(comment_texts, nlp))
        else:
            comment_vectors = np.array([])
            comment_urls = []
            
        # Process bluesky posts in parallel if they exist
        if bluesky_posts:
            future_bluesky_data = executor.submit(lambda: (
                [preprocess_text(post.get("text", "")) for post in bluesky_posts],
                [post.get("url", "") for post in bluesky_posts]
            ))
            bluesky_texts, bluesky_urls = future_bluesky_data.result()
            future_bluesky_vectors = executor.submit(lambda: batch_vectorize(bluesky_texts, nlp))
        else:
            bluesky_vectors = np.array([])
            bluesky_urls = []
        
        # Get all vectorized results
        keyword_vectors = future_keyword_vectors.result()
        a_vectors = future_a_vectors.result()
        b_vectors = future_b_vectors.result()
        dilemma_title_vectors = future_title_vectors.result()
        
        if articles:
            article_vectors = future_article_vectors.result()
        if comments:
            comment_vectors = future_comment_vectors.result()
        if bluesky_posts:
            bluesky_vectors = future_bluesky_vectors.result()
    
    # Convert numpy arrays to torch tensors for GPU computation
    keyword_tensors = torch.tensor(keyword_vectors, device=DEVICE)
    a_tensors = torch.tensor(a_vectors, device=DEVICE)
    b_tensors = torch.tensor(b_vectors, device=DEVICE)
    
    # Compute similarities on GPU
    sim_a = torch.matmul(a_tensors, keyword_tensors.T).cpu().numpy()
    sim_b = torch.matmul(b_tensors, keyword_tensors.T).cpu().numpy()
    mask = (sim_a > threshold) & (sim_b > threshold)
    
    # Compute article and comment similarities on GPU if they exist
    if articles:
        article_tensors = torch.tensor(article_vectors, device=DEVICE)
        dilemma_title_tensors = torch.tensor(dilemma_title_vectors, device=DEVICE)
        article_similarities = torch.matmul(dilemma_title_tensors, article_tensors.T).cpu().numpy()
    
    if comments:
        comment_tensors = torch.tensor(comment_vectors, device=DEVICE)
        comment_similarities = torch.matmul(keyword_tensors, comment_tensors.T).cpu().numpy()
        
    if bluesky_posts:
        bluesky_tensors = torch.tensor(bluesky_vectors, device=DEVICE)
        bluesky_similarities = torch.matmul(keyword_tensors, bluesky_tensors.T).cpu().numpy()

    matched_dilemmas = []

    # Process matches efficiently
    for i, (_, row) in enumerate(dilemmas_df.iterrows()):
        matches = np.where(mask[i])[0]  # Indices of matching keywords
        if matches.size == 0:
            continue

        keyword = keywords[matches[0]]  # Take first match
        keyword_vector = keyword_vectors[matches[0]]
        keyword_idx = matches[0]

        # Base metadata
        metadata_dict = {
            "priority_data": {
                "ENV": row["ENV"],
                "ECO": row["ECO"],
                "SOC": row["SOC"],
                "TECH": row["TECH"],
            }
        }

        # Process articles (batch similarity)
        article_metadata = None
        if articles and article_similarities.size > 0:
            similarities = article_similarities[i]  # Similarities for this dilemma
            best_idx = np.argmax(similarities)
            best_similarity = similarities[best_idx]
            if best_similarity > threshold:
                article_metadata = {
                    "title": article_titles[best_idx],
                    "url": article_urls[best_idx]
                }

        # Process comments (batch similarity)
        youtube_metadata = None
        if comments and comment_similarities.size > 0:
            similarities = comment_similarities[keyword_idx]  # Similarities for this keyword
            best_idx = np.argmax(similarities)
            best_similarity = similarities[best_idx]
            if best_similarity > threshold:
                youtube_metadata = { 
                    "comment": comment_texts[best_idx],
                    "url": comment_urls[best_idx]
                }
                
        # Process bluesky posts (batch similarity)
        bluesky_metadata = None
        if bluesky_posts and bluesky_similarities.size > 0:
            similarities = bluesky_similarities[keyword_idx]  # Similarities for this keyword
            best_idx = np.argmax(similarities)
            best_similarity = similarities[best_idx]
            if best_similarity > threshold:
                bluesky_metadata = {
                    "text": bluesky_texts[best_idx],
                    "url": bluesky_urls[best_idx]
                }




        # Merge metadata
        merged_metadata = {
            "reference": {
                "printed_media": article_metadata,
                "social_media": {
                    "url": [
                        youtube_metadata["url"] for youtube_metadata in [youtube_metadata] if youtube_metadata and "url" in youtube_metadata
                    ] + [
                        bluesky_metadata["url"] for bluesky_metadata in [bluesky_metadata] if bluesky_metadata and "url" in bluesky_metadata
                    ]
                }
            },
            **metadata_dict,
        }

        # Append matched dilemma
        matched_dilemmas.append({
            "dilemma_serial_id": row["dilemma_serial_id"],
            "title": row["title"],
            "column_a": row["column_a"],
            "column_b": row["column_b"],
            "description": row["description"],
            "keyword": keyword,
            "metadata": merged_metadata,
        })

    return pd.DataFrame(matched_dilemmas)



# def match_keywords_with_dilemmas(dilemmas_df,  keywords, threshold=0.74):
#     matched_dilemmas = []
     
#     for _, dilemma in dilemmas_df.iterrows():
#         # Preprocess and vectorize `column_a` and `column_b`
#         column_a_vector = nlp(preprocess_text(dilemma["column_a"])).vector
#         column_b_vector = nlp(preprocess_text(dilemma["column_b"])).vector
#         # Check similarity with each keyword
#         for keyword in keywords:
#             keyword_vector = nlp(preprocess_text(keyword)).vector
#             sim_a = cosine_similarity([column_a_vector], [keyword_vector])[0][0]
#             sim_b = cosine_similarity([column_b_vector], [keyword_vector])[0][0]

#             # Include the dilemma only if both scores exceed the threshold
#             if sim_a > threshold and sim_b > threshold:
#                 matched_dilemmas.append({
#                     "title": dilemma["title"],
#                     "column_a": dilemma["column_a"],
#                     "column_b": dilemma["column_b"],
#                     "description": dilemma["description"],
#                     "keyword": keyword,
#                     "metadata": {
#                         "ENV": dilemma["ENV"],
#                         "ECO": dilemma["ECO"],
#                         "SOC": dilemma["SOC"],
#                         "TECH": dilemma["TECH"],
#                     },
#                 })

#                 break  # Stop checking other keywords once a match is found for this dilemma

#     return pd.DataFrame(matched_dilemmas)




def extract_columns_as_lists(df):
   
    # Extract column_a and column_b as lists
    column_a_list = df["column_a"].tolist()
    column_b_list = df["column_b"].tolist()

    # Combine both lists into a single list
    combined_list = column_a_list + column_b_list

    return combined_list


def filter_keywords_with_user_keywords(final_keywords, user_keywords, threshold=0.75):
    """
    Filters final_keywords by keeping only those semantically similar to any user_keywords.

    Args:
        final_keywords (list of str): Extracted keywords.
        user_keywords (list of str): Input list from the user.
        threshold (float): Cosine similarity threshold (0 to 1).

    Returns:
        list of str: Filtered keywords based on semantic similarity.
    """
    filtered_keywords = []

    user_vectors = [nlp(kw).vector for kw in user_keywords if kw.strip()]
    if not user_vectors:
        return []

    for candidate in final_keywords:
        cand_vector = nlp(candidate).vector
        similarities = cosine_similarity([cand_vector], user_vectors)[0]
        if np.max(similarities) >= threshold:
            filtered_keywords.append(candidate)

    return filtered_keywords