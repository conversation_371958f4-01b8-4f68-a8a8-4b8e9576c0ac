from docx import Document
import pandas as pd
 
def read_dilemmas_with_description(file_path):
    doc = Document(file_path)
    dilemmas = []

    for para in doc.paragraphs:
        text = para.text.strip()
        if " vs. " in text:  # Only process dilemmas in "X vs. Y" format
            parts = text.split(" vs. ", 1)
            if len(parts) == 2:
                column_a = parts[0].strip()
                column_b_with_desc = parts[1].strip()

                # Extract column_b and description
                column_b, *description_parts = column_b_with_desc.split(":", 1)
                column_b = column_b.strip()
                description = description_parts[0].strip() if description_parts else ""

                # Create the title with "column_a vs. column_b:"
                title = f"{column_a} vs. {column_b}"
                print("column_a vs column_b ", f"{column_a}  vs. {column_b}")
                dilemmas.append({
                    "title": title,
                    "column_a": column_a,
                    "column_b": column_b,
                    "description": description,
                })

    return pd.DataFrame(dilemmas)


def read_dilemmas_from_excel(file_path):
    # Read the Excel file into a pandas DataFrame
    df = pd.read_excel(file_path)

    # Check if the necessary columns are in the file
    required_columns = [
        'dilemma_serial_id',
        'title', 'description', 
        'ENV', 'ECO', 
        'SOC', 'TECH'
    ]
    
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"Missing required column: {col}")

    # Split the 'Dilemma Title' into 'column_a' and 'column_b'
    # Assuming the format is "X vs. Y"
    df[['column_a', 'column_b']] = df['title'].str.split(' vs. ', expand=True)

    # Reorder columns to include 'column_a' and 'column_b' and other required ones
    dilemmas = df[['column_a', 'column_b', 'dilemma_serial_id', 'title', 'description', 
        'ENV', 'ECO', 
        'SOC', 'TECH']].to_dict(orient="records")
    
    return pd.DataFrame(dilemmas)
