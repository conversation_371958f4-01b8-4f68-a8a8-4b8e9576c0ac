from lib.processors.keyword_processor import KeywordProcessor

class BlueskyProcessor:
    def __init__(self,  climate_keywords=None, num_topics=5, top_k=10, similarity_threshold=0.7):
        
        self.keyword_processor = KeywordProcessor(climate_keywords, num_topics, top_k, similarity_threshold)
        

    def process_posts(self, posts):
        """
        Process tweets from pre-fetched data.
        """
        if posts:
            # Merge all tweet texts into a single string
            combined_text = " ".join([post.get("text", "") for post in posts])

            # Extract keywords from the combined text
            keywords = self.keyword_processor.process_text(combined_text)
            return keywords
        return []