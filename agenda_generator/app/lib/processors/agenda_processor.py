import torch
from transformers import GPT2LMHeadModel, GPT2Tokenizer
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
from sklearn.metrics import silhouette_score
import re
from concurrent.futures import ThreadPoolExecutor

# Check if CUDA is available and set device accordingly
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {DEVICE}")

# Load model and tokenizer
TOKENIZER = GPT2Tokenizer.from_pretrained("gpt2-medium")
MODEL = GPT2LMHeadModel.from_pretrained("gpt2-medium").eval().to(DEVICE)
class AgendaGenerator:
    def __init__(self):
        self.tokenizer = TOKENIZER
        self.model = MODEL
        self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Log device information
        print(f"Model loaded on: {next(self.model.parameters()).device}")

    def generate_agenda_title(self, keywords):
        prompt = (
            "Generate a clear and concise session title (max 10 words) using the following keywords:\n"
            f"Keywords: {', '.join(keywords)}\n"
            "Session Title:"
        )
        try:
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", max_length=100, truncation=True)
            # Move inputs to the same device as the model
            inputs = inputs.to(DEVICE)
            
            with torch.no_grad():  # Disable gradient calculation for inference
                outputs = self.model.generate(
                    inputs,
                    max_length=300,
                    num_return_sequences=1,
                    no_repeat_ngram_size=2,
                    num_beams=5,
                    early_stopping=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            title = generated_text.split("Session Title:")[-1].strip()
            
            # Clean up GPU memory
            if DEVICE.type == "cuda":
                torch.cuda.empty_cache()
                
            return title.split('\n')[0].split('.')[0]
        except Exception as e:
            return f"Error generating title: {e}"

    def find_optimal_clusters(self, keywords, max_clusters=10):
        # Move TF-IDF computation to GPU using CuPy if available
        try:
            import cupy as cp
            use_gpu = True
        except ImportError:
            use_gpu = False

        vectorizer = TfidfVectorizer(stop_words="english")
        X = vectorizer.fit_transform(keywords)
        
        if use_gpu:
            X_gpu = cp.asarray(X.toarray())
        
        # Parallel processing for clustering
        def process_cluster(k):
            kmeans = KMeans(n_clusters=k, random_state=42, n_init='auto')
            if use_gpu:
                labels = kmeans.fit_predict(cp.asnumpy(X_gpu))
            else:
                labels = kmeans.fit_predict(X)
            
            inertia = kmeans.inertia_
            sil_score = silhouette_score(X, labels) if k > 1 else -1
            return inertia, sil_score

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=8) as executor:
            results = list(executor.map(process_cluster, range(1, max_clusters + 1)))
        
        distortions, silhouette_scores = zip(*results)
        optimal_clusters = silhouette_scores.index(max(silhouette_scores)) + 1
        
        # Clean up GPU memory if used
        if use_gpu:
            del X_gpu
            cp.get_default_memory_pool().free_all_blocks()
        
        print(f"Optimal number of clusters based on Silhouette Score: {optimal_clusters}")
        return optimal_clusters

    def cluster_keywords(self, keywords):
        # Optimize vectorization for GPU
        try:
            import cupy as cp
            use_gpu = True
        except ImportError:
            use_gpu = False

        vectorizer = TfidfVectorizer(stop_words="english")
        X = vectorizer.fit_transform(keywords)
        
        if use_gpu:
            X_gpu = cp.asarray(X.toarray())
        
        n_clusters = self.find_optimal_clusters(keywords)
        
        # Initialize KMeans with optimized settings
        kmeans = KMeans(
            n_clusters=n_clusters,
            random_state=42,
            n_init='auto',  # Automatically determine the number of initializations
            algorithm='elkan'  # More efficient for GPU
        )
        
        # Fit KMeans using GPU if available
        if use_gpu:
            labels = kmeans.fit_predict(cp.asnumpy(X_gpu))
            del X_gpu
            cp.get_default_memory_pool().free_all_blocks()
        else:
            labels = kmeans.fit_predict(X)
        
        # Efficiently create clusters using list comprehension
        clusters = [[] for _ in range(n_clusters)]
        for i, label in enumerate(labels):
            clusters[label].append(keywords[i])
        
        return clusters

    def filter_agenda_titles(self, raw_titles):
        """
        Filter and refine the generated agenda titles for better cohesion and consistency.
        :param raw_titles: List of raw agenda titles (strings)
        :return: List of filtered and refined agenda titles
        """
        filtered_titles = []

        for title in raw_titles:
            # Remove overly verbose or generic session descriptions
            title = re.sub(r"(?i)(this session will discuss|session description[:\-]?)", "", title)

            # Remove excessive whitespace
            title = " ".join(title.split())

            # Ensure consistent casing (capitalize first letter of each word)
            title = title.capitalize()

            # Trim overly long titles to a reasonable length (e.g., max 15 words)
            words = title.split()
            if len(words) > 20:
                title = " ".join(words[:20]) + "..."

            # Remove placeholder-like titles (e.g., NET-ZERO-CONTRIBUTIONS-SESSION-TITLE)
            if "net-zero" in title.lower() or "session" in title.lower():
                continue

            # Append to the filtered list if it passes all checks
            filtered_title = title.strip()
            if title.strip():
                filtered_titles.append(filtered_title[1:-1])

        return filtered_titles
    