
import re
import spacy
import numpy as np
import torch
import gensim
from gensim import corpora
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
from keybert import KeyBERT
import gc

# Check if CUDA is available
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {DEVICE}")

# Enable CUDA for torch operations
torch.set_default_tensor_type('torch.cuda.FloatTensor' if torch.cuda.is_available() else 'torch.FloatTensor')


MODEL = 'all-MiniLM-L6-v2'
EMBEDDING_MODEL = SentenceTransformer(MODEL).to(DEVICE)
KW_MODEL = KeyBERT(model=MODEL)
NLP = spacy.load('en_core_web_md')  # Load spaCy model

# Enable GPU if available for spaCy
try:
    if torch.cuda.is_available():
        spacy.require_gpu()
        print("GPU acceleration enabled for spaCy")
except Exception as e:
    print(f"GPU acceleration not available for spaCy: {e}")
    print("Falling back to CPU processing for spaCy")

print("Models loaded")
class KeywordProcessor:
    def __init__(self, climate_keywords=None, num_topics=5, top_k=10, similarity_threshold=0.74):
        self.climate_keywords = climate_keywords or []
        self.num_topics = num_topics
        self.top_k = top_k
        self.similarity_threshold = similarity_threshold
        # Initialize models
        self.embedding_model = EMBEDDING_MODEL
        self.kw_model = KW_MODEL
      
        self.nlp = NLP
      

    def preprocess_text(self, text):
        """
        Preprocess text: lowercasing and removing punctuation.
        """
        if not text:
            return ""
        return re.sub(r'[^\w\s]', '', text.lower().strip())

    def get_vector(self, text):
        """
        Get the vector representation of the text using spaCy's NLP model.
        Returns a CUDA tensor if available, otherwise CPU tensor.
        """
        if self.nlp:
            doc = self.nlp(self.preprocess_text(text))
            vector = torch.tensor(doc.vector, dtype=torch.float32, device=DEVICE)
            return vector
        else:
            print("spaCy NLP model is not loaded. Returning a zero vector.")
            return torch.zeros(300, dtype=torch.float32, device=DEVICE)

    def extract_keywords(self, text):
        """
        Extract keywords using KeyBERT.
        """
        keywords = self.kw_model.extract_keywords(text, keyphrase_ngram_range=(1, 2), stop_words='english', top_n=self.top_k * 3)
        return [kw[0] for kw in keywords]

    def perform_lda_topic_modeling(self, texts):
        """
        Perform LDA topic modeling to extract topics.
        """
        data_words = [gensim.utils.simple_preprocess(text, deacc=True) for text in texts]
        id2word = corpora.Dictionary(data_words)
        corpus = [id2word.doc2bow(text) for text in data_words]

        lda_model = gensim.models.LdaModel(corpus=corpus, id2word=id2word, num_topics=self.num_topics, passes=10)

        topics = lda_model.show_topics(num_topics=self.num_topics, formatted=False)
        lda_keywords = [word for topic in topics for word, _ in topic[1]]
        return lda_keywords

    def filter_keywords_by_spacy_similarity(self, lda_keywords, extracted_keywords):
        """
        Filter keywords based on spaCy vector similarity using GPU acceleration.
        """
        filtered_keywords = []
        
        # Convert to tensors and move to GPU if available
        lda_vectors = torch.stack([self.get_vector(lda_kw) for lda_kw in lda_keywords])
        extracted_vectors = torch.stack([self.get_vector(ex_kw) for ex_kw in extracted_keywords])
        
        # Compute similarities using matrix multiplication on GPU
        similarities = torch.matmul(lda_vectors, extracted_vectors.T)
        
        # Process results
        for i, lda_kw in enumerate(lda_keywords):
            mask = similarities[i] > 0.5
            # Explicitly convert tensor to numpy array using .cpu().to('cpu').numpy()
            mask_np = mask.cpu().to('cpu').numpy()
            filtered_keywords.extend([kw for kw, m in zip(extracted_keywords, mask_np) if m])
        
        return list(set(filtered_keywords))


    def filter_keywords_by_climate_similarity(self, filtered_keywords):
        """
        Filter keywords by similarity to predefined climate-related keywords using GPU acceleration.
        If fewer than 10 keywords meet the threshold, include the next best matches (up to 15 total).
        """
        # Convert all vectors to tensors and move to GPU
        climate_vectors = torch.stack([torch.tensor(self.nlp(self.preprocess_text(c_kw)).vector, dtype=torch.float32, device=DEVICE)
                                     for c_kw in self.climate_keywords])
        keyword_vectors = torch.stack([torch.tensor(self.nlp(self.preprocess_text(keyword)).vector, dtype=torch.float32, device=DEVICE)
                                     for keyword in filtered_keywords])
        
        # Normalize vectors for cosine similarity
        climate_vectors = torch.nn.functional.normalize(climate_vectors, p=2, dim=1)
        keyword_vectors = torch.nn.functional.normalize(keyword_vectors, p=2, dim=1)
        
        # Compute similarities using matrix multiplication
        similarities = torch.matmul(keyword_vectors, climate_vectors.T)
        max_similarities, _ = torch.max(similarities, dim=1)
        
        # Convert to CPU for processing results
        max_similarities = max_similarities.cpu().to('cpu').numpy()
        keyword_similarities = list(zip(filtered_keywords, max_similarities))

        # Sort keywords by similarity (descending)
        keyword_similarities.sort(key=lambda x: x[1], reverse=True)

        # Take keywords above threshold first
        filtered_climate_keywords = [kw for kw, sim in keyword_similarities if sim >= self.similarity_threshold]

        # If fewer than 10, add more (up to 15) even if below threshold (but still best available)
        if len(filtered_climate_keywords) < 10:
            remaining_keywords = [kw for kw, sim in keyword_similarities if kw not in filtered_climate_keywords]
            num_needed = min(15 - len(filtered_climate_keywords), len(remaining_keywords))
            filtered_climate_keywords.extend(remaining_keywords[:num_needed])

        return filtered_climate_keywords[:15]  # Ensure no more than 15
    def process_text(self, text):
        """
        Extract keywords and topics from the provided text.
        """
        # Step 1: Extract Keywords using KeyBERT
        extracted_keywords = self.extract_keywords(text)

        # Step 2: Perform LDA Topic Modeling
        lda_keywords = self.perform_lda_topic_modeling([text])

        # Step 3: Filter keywords using spaCy similarity
        filtered_keywords = self.filter_keywords_by_spacy_similarity(lda_keywords, extracted_keywords)

        # Step 4: Filter keywords by climate-related topics
        final_keywords = self.filter_keywords_by_climate_similarity(filtered_keywords)


        return final_keywords