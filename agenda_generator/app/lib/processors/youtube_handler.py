from lib.processors.keyword_processor import KeywordProcessor

class YouTubeProcessor:
    def __init__(self,  climate_keywords=None, num_topics=5, top_k=10, similarity_threshold=0.7):
        
        self.keyword_processor = KeywordProcessor(climate_keywords, num_topics, top_k, similarity_threshold)
        
        self.comments_with_urls = []
        self.results = []

    def process_youtube_comments(self, comments):
        """
        Process YouTube comments from pre-fetched data.
        """
        self.comments_with_urls = comments
        if comments:
            # Extract keywords from the comments
            comments_text = [item["comment"] for item in comments]
            final_keywords = self.keyword_processor.process_text(' '.join(comments_text))
            self.results.append(final_keywords)

    def get_results(self):
        """
        Return the final results.
        """
        return self.results
    
    def get_comments(self):
        """
        Return the final results.
        """
        return self.comments_with_urls