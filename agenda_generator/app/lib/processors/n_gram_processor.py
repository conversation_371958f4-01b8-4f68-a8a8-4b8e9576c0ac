import io
import re
import base64
import pandas as pd
import matplotlib.pyplot as plt
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.util import ngrams
from collections import Counter
from nltk.stem import PorterStemmer

class NGramProcessor:
    def __init__(self,  n=2, top_k=20):
        self.total_page_contents = []
        self.stop_words = set(stopwords.words('english'))
        self.more_stopwords = [
            'call', 'upon', 'still', 'nevertheless', 'down', 'every', 'forty', '‘re', 'always', 'whole', 'side', "n't",'now', 'however', 'an', 'show', 'least', 'give', 'below', 'did', 'sometimes', 'which', "'s", 'nowhere',
            'per', 'hereupon','yours', 'she', 'moreover', 'eight', 'somewhere', 'within', 'whereby', 'few', 'has', 'so', 'have', 'for', 'noone','top', 'were', 'those', 'thence', 'eleven', 'after', 'no', '’ll', 'others', 'ourselves', 'themselves', 'though',
            'that', 'nor', 'just', '’s', 'before', 'had', 'toward', 'another', 'should', 'herself', 'and', 'these', 'such',
            'elsewhere', 'further', 'next', 'indeed', 'bottom', 'anyone', 'his', 'each', 'then', 'both', 'became', 'third',
            'whom', '‘ve', 'mine', 'take', 'many', 'anywhere', 'to', 'well', 'thereafter', 'besides', 'almost', 'front','fifteen', 'towards', 'none', 'be', 'herein', 'two', 'using', 'whatever', 'please', 'perhaps', 'full', 'ca', 'we',
            'latterly', 'here', 'therefore', 'us', 'how', 'was', 'made', 'the', 'or', 'may', '’re', 'namely', "'ve", 'anyway','amongst', 'used', 'ever', 'of', 'there', 'than', 'why', 'really', 'whither', 'in', 'only', 'wherein', 'last',
            'under', 'own', 'therein', 'go', 'seems', '‘m', 'wherever', 'either', 'someone', 'up', 'doing', 'on', 'rather',
            'ours', 'again', 'same', 'over', '‘s', 'latter', 'during', 'done', "'re", 'put', "'m", 'much', 'neither', 'among',
            'seemed', 'into', 'once', 'my', 'otherwise', 'part', 'everywhere', 'never', 'myself', 'must', 'will', 'am', 'can',
            'else', 'although', 'as', 'beyond', 'are', 'too', 'becomes', 'does', 'a', 'everyone', 'but', 'some', 'regarding',
            '‘ll', 'against', 'throughout', 'yourselves', 'him', "'d", 'it', 'himself', 'whether', 'move', '’m', 'hereafter',
            're', 'while', 'whoever', 'your', 'first', 'amount', 'twelve', 'serious', 'other', 'any', 'off', 'seeming', 'four',
            'itself', 'nothing', 'beforehand', 'make', 'out', 'very', 'already', 'various', 'until', 'hers', 'they', 'not',
            'them', 'where', 'would', 'since', 'everything', 'at', 'together', 'yet', 'more', 'six', 'back', 'with',
            'thereupon', 'becoming', 'around', 'due', 'keep', 'somehow', 'n‘t', 'across', 'all', 'when', 'i', 'empty', 'nine',
            'five', 'get', 'see', 'been', 'name', 'between', 'hence', 'ten', 'several', 'from', 'whereupon', 'through',
            'hereby', "'ll", 'alone', 'something', 'formerly', 'without', 'above', 'onto', 'except', 'enough', 'become',
            'behind', '’d', 'its', 'most', 'n’t', 'might', 'whereas', 'anything', 'if', 'her', 'via', 'fifty', 'is',
            'thereby', 'twenty', 'often', 'whereafter', 'their', 'also', 'anyhow', 'cannot', 'our', 'could', 'because', 'who',
            'beside', 'by', 'whence', 'being', 'meanwhile', 'this', 'afterwards', 'whenever', 'mostly', 'what', 'one', 'nobody',
            'seem', 'less', 'do', '‘d', 'say', 'thus', 'unless', 'along', 'yourself', 'former', 'thru', 'he', 'hundred', 'three',
            'sixty', 'me', 'sometime', 'whose', 'you', 'quite', '’ve', 'about', 'even', '0', '1', '2', '3', '4', '5', '6', '7',
            '8', '9', 'a', 'A', 'about', 'above', 'across', 'after', 'again', 'against', 'all', 'almost', 'alone', 'along',
            'already', 'also', 'although', 'always', 'am', 'among', 'an', 'and', 'another', 'any', 'anyone', 'anything',
            'anywhere', 'are', "aren't", "around", "as", "at", 'b', 'B', "back", "be", "became", "because", "become",
            'becomes', 'been', 'before', 'behind', 'being', 'below', 'between', 'both', 'but', 'by', 'c', 'C', 'can', 'cannot',
            "can't", 'could', "couldn't", 'd', 'D', 'did', "didn't", 'do', 'does', "doesn't", 'doing', 'done', "don't", 'down',
            'during', 'e', 'E', 'each', 'either', 'enough', 'even', 'ever', 'every', 'everyone', 'everything', 'everywhere',
            'f', 'F', 'few', 'find', 'first', 'for', 'four', 'from', 'full', 'further', 'g', 'G', 'get', 'give', 'go', 'h', 'H',
            'had', "hadn't", 'has', "hasn't", 'have', "haven't", 'having', 'he', "he'd", "he'll", 'her', 'here', "here's",
            'hers', 'herself', "he's", 'him', 'himself', 'his', 'how', 'however', "how's", 'i', 'I', "i'd", 'if', "i'll", "i'm",
            'in', 'interest', 'into', 'is', "isn't", 'it', "it's", 'its', 'itself', "i've", 'j', 'J', 'k', 'K', 'keep', 'l', 'L',
            'last', 'least', 'less', "let's", 'm', 'M', 'made', 'many', 'may', 'me', 'might', 'more', 'most', 'mostly', 'much',
            'must', "mustn't", 'my', 'myself', 'n', 'N', 'never', 'next', 'no', 'nobody', 'noone', 'nor', 'not', 'nothing',
            'now', 'nowhere', 'o', 'O', 'of', 'off', 'often', 'on', 'once', 'one', 'only', 'or', 'other', 'others', 'ought',
            'our', 'ours', 'ourselves', 'out', 'over', 'own', 'p', 'P', 'part', 'per', 'perhaps', 'put', 'q', 'Q', 'r', 'R',
            'rather', 's', 'S', 'same', 'see', 'seem', 'seemed', 'seeming', 'seems', 'several', "shan't", 'she', "she'd",
            "she'll", "she's", 'should', "shouldn't", 'show', 'side', 'since', 'so', 'some', 'someone', 'something',
            'somewhere', 'still', 'such', 't', 'T', 'take', 'than', 'that', "that's", 'the', 'their', 'theirs', 'them',
            'themselves', 'then', 'there', 'therefore', "there's", 'these', 'they', "they'd", "they'll", "they're", "they've",
            'this', 'those', 'though', 'three', 'through', 'thus', 'to', 'together', 'too', 'toward', 'two', 'u', 'U', 'under',
            'until', 'up', 'upon', 'us', 'v', 'V', 'very', 'w', 'W', 'was', "wasn't", 'we', "we'd", "we'll", 'well', "we're",
            'were', "weren't", "we've", 'what', "what's", 'when', "when's", 'where', "where's", 'whether', 'which', 'while',
            'who', 'whole', 'whom', "who's", 'whose', 'why', "why's", 'will', 'with', 'within', 'without', "won't", 'would',
            "wouldn't", 'x', 'X', 'y', 'Y', 'yet', 'you', "you'd", "you'll", 'your', "you're", 'yours', 'yourself', 'yourselves',
            "you've", 'z', 'Z', "al", "et", "vol", "pp", "time", "ieee", "traf", "new" # Add more stopwords here
        ]
        self.stop_words.update(self.more_stopwords)
        self.text_df = pd.DataFrame(columns=[ 'text'])
        self.n = n
        self.top_k = top_k
        self.stemmer = PorterStemmer()

    def create_text_dataframe(self, comments, articles, twitter_tweets, grouped_pdf_texts, bluesky_posts):
        texts = []
        
        # Process comments
        for comment in comments:
            if isinstance(comment, dict) and 'comment' in comment:
                texts.append(comment['comment'])
        
        # Process articles
        for article in articles:
            if isinstance(article, dict):
                title = article.get('title', '')
                description = article.get('description', '')
                text = f"{title} {description}".strip()
                if text:
                    texts.append(text)
        
        # Process tweets
        for tweet in twitter_tweets:
            if isinstance(tweet, dict) and 'text' in tweet:
                texts.append(tweet['text'])
        
        # Process PDF texts
        for year, year_texts in grouped_pdf_texts.items():
            texts.extend(year_texts)
        
        # Process bluesky posts
        for post in bluesky_posts:
            if isinstance(post, dict) and 'text' in post:
                texts.append(post['text'])
        
        self.text_df = pd.DataFrame({'text': texts})
    

    @staticmethod
    def preprocess_text(text):
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n+', ' ', text)
        text = re.sub(r'[^\x00-\x7F]+', ' ', text)
        pattern = r'https://\S+'
        text = re.sub(pattern, '', text)
        pattern = r'DOI: \S+'
        text = re.sub(pattern, '', text)
        pattern = r'et al.\S+'
        text = re.sub(pattern, '', text)
        text = text.strip()
        text = re.sub('[,\.!?]', '', text)
        return text

    def preprocess_texts(self):
        self.text_df['text'] = self.text_df['text'].apply(lambda x: NGramProcessor.preprocess_text(x).lower())
        return self.text_df

    def preprocess_text_for_ngrams(self, text):
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n+', ' ', text)
        text = re.sub(r'[^\x00-\x7F]+', ' ', text)
        text = re.sub(r'https://\S+', '', text)
        text = re.sub(r'DOI: \S+', '', text)
        text = re.sub(r'et al\.\S+', '', text)
        text = text.strip()
        text = re.sub('[,\.!?]', '', text)

        tokens = word_tokenize(text.lower())
        tokens = [word for word in tokens if word.isalpha()]

        if self.n < 3:
            tokens = [word for word in tokens if word not in self.stop_words]

        return tokens

    def plot_ngram_frequency(self):
        
        self.preprocess_texts()
        long_text = ' '.join(self.text_df['text'].tolist())
        tokens = self.preprocess_text_for_ngrams(long_text)

        stem_to_original = {}
        stemmed_tokens = []
        for token in tokens:
            stemmed = self.stemmer.stem(token)
            stemmed_tokens.append(stemmed)
            if stemmed not in stem_to_original:
                stem_to_original[stemmed] = token

        n_grams = list(ngrams(stemmed_tokens, self.n))
        freq_dist = Counter(n_grams)

        common_n_grams = freq_dist.most_common()

        # Function to clean n-grams
        def clean_ngram(ngram):
            # Remove stopwords from the start and end
            ngram = list(ngram)
            while ngram and ngram[0] in self.stop_words:
                ngram = ngram[1:]
            while ngram and ngram[-1] in self.stop_words:
                ngram = ngram[:-1]
            return ngram

        # Clean and fill each n-gram
        filled_n_grams = []
        for gram, count in common_n_grams:
            cleaned_gram = clean_ngram(gram)
            if len(cleaned_gram) < self.n:
                original_tokens = list(gram)
                # Fill from the start
                for token in original_tokens:
                    if token not in cleaned_gram and token not in self.stop_words:
                        cleaned_gram.insert(0, token)
                    if len(cleaned_gram) == self.n:
                        break
                # Fill from the end if still not enough length
                if len(cleaned_gram) < self.n:
                    for token in original_tokens:
                        if token not in cleaned_gram and token not in self.stop_words:
                            cleaned_gram.append(token)
                        if len(cleaned_gram) == self.n:
                            break
            if len(cleaned_gram) == self.n:
                filled_n_grams.append((tuple(cleaned_gram), count))

        # Sort and select the top `top_k` n-grams by frequency
        filled_n_grams = sorted(filled_n_grams, key=lambda x: x[1], reverse=True)[:self.top_k]

        # Unpack the filled n-grams for plotting
        if filled_n_grams:
            n_grams, counts = zip(*filled_n_grams)
            n_grams = [' '.join(stem_to_original[token] for token in gram) for gram in n_grams]
        else:
            n_grams, counts = [], []

        plt.figure(figsize=(12, 8), dpi=100)
        plt.barh(n_grams, counts, color='skyblue')
        plt.xlabel('Frequency')
        plt.ylabel('N-grams')
        plt.title(f'Top {self.top_k} most common {self.n}-grams')
        plt.gca().invert_yaxis()

        buf = io.BytesIO()
        plt.savefig(buf, format='png', bbox_inches='tight')
        buf.seek(0)
        base64_png = base64.b64encode(buf.getvalue()).decode('utf-8')
        buf.close()
        plt.close()

        return base64_png
