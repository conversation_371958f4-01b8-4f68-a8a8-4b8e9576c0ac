
from lib.processors.keyword_processor import KeywordProcessor



class NewsProcessor:
    def __init__(self, climate_keywords=None, num_topics=5, top_k=10, similarity_threshold=0.7):
        
        self.keyword_processor = KeywordProcessor(climate_keywords, num_topics, top_k, similarity_threshold)
        
        self.results = []

    def process_articles(self, articles):
        """
        Process articles: merge all articles into a single text and extract keywords.
        """
        
        # Merge all article texts into a single string
        combined_text = " ".join([article.get("description", "") for article in articles])
       

        # Extract keywords from the combined text
        keywords = self.keyword_processor.process_text(combined_text)
        return keywords