import os
import re
from lib.processors.keyword_processor import KeywordProcessor
import pycountry
from PyPDF2 import PdfReader
import requests
import tempfile
import os
import tempfile
import requests
from PyPDF2 import PdfReader
import pycountry

class PDFProcessor:
    def __init__(self, pdf_file_links, climate_keywords, num_topics=5, top_k=10, similarity_threshold=0.9):
        self.pdf_file_links = pdf_file_links

        # Initialize KeywordProcessor
        self.keyword_processor = KeywordProcessor(climate_keywords, num_topics, top_k, similarity_threshold)

        # For storing intermediate results
        self.grouped_texts_by_country_year = {}
        self.grouped_texts_by_year = {}
        self.results = []
        self.all_keywords = []

    def extract_text_and_year_from_pdf(self, pdf_path):
        """
        Extract text and year from a PDF file.
        """
        try:
            with open(pdf_path, 'rb') as file:
                reader = PdfReader(file)
                text = ''
                for page in reader.pages:
                    text += page.extract_text() or ''

                # Extract year from metadata or filename
                metadata = reader.metadata
                year = self.extract_year_from_metadata(metadata) or self.extract_year_from_filename(os.path.basename(pdf_path))
                return text, year
        except Exception as e:
            print(f"Error reading PDF {pdf_path}: {e}")
            return '', None

    def extract_year_from_metadata(self, metadata):
        if metadata:
            date_str = metadata.get('/CreationDate') or metadata.get('/ModDate')
            if date_str:
                match = re.match(r'D:(\d{4})', date_str)
                if match:
                    return int(match.group(1))
        return None

    def extract_year_from_filename(self, filename):
        match = re.search(r'(\d{4})', filename)
        return int(match.group(1)) if match else None

    def count_country_mentions(self, text):
        """
        Count mentions of countries in the text.
        """
        country_list = [country.name for country in pycountry.countries]
        country_aliases = {
            'usa': 'United States',
            'u.s.': 'United States',
            'u.s.a.': 'United States',
            'uk': 'United Kingdom',
            'u.k.': 'United Kingdom',
            'south korea': 'Korea, Republic of',
            'north korea': "Korea, Democratic People's Republic of",
            'russia': 'Russian Federation',
            'iran': 'Iran, Islamic Republic of',
            'venezuela': 'Venezuela, Bolivarian Republic of',
        }
        country_names = set([name.lower() for name in country_list] + list(country_aliases.keys()))
        country_counts = {}

        for country in country_names:
            pattern = r'\b' + re.escape(country) + r'\b'
            matches = re.findall(pattern, text.lower())
            if matches:
                standardized_name = country_aliases.get(country, country.title())
                count = len(matches)
                country_counts[standardized_name] = country_counts.get(standardized_name, 0) + count

        return country_counts

    def process_pdfs(self):
        """
        Process all PDFs from the provided external file links and group texts by country and year.
        """
        pdf_file_links = self.pdf_file_links
        temp_pdf_path = ''
        if not pdf_file_links:
            print("No PDF file links provided.")
            return

        for pdf_url in pdf_file_links:
            filename = os.path.basename(pdf_url)
            print(f"\nProcessing {pdf_url}...")

            try:
                # Download the PDF to a temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_pdf:
                    response = requests.get(pdf_url, stream=True)
                    print(response)
                    if response.status_code == 200:
                        for chunk in response.iter_content(chunk_size=1024):
                            temp_pdf.write(chunk)
                    else:
                        print(f"Failed to download {filename}. HTTP Status Code: {response.status_code}")
                        continue

                    temp_pdf_path = temp_pdf.name

                # Extract text and year from the downloaded PDF
                text, year = self.extract_text_and_year_from_pdf(temp_pdf_path)
                if not text:
                    continue

                # Assign 'Unknown' if the year is not found
                year = year or 'Unknown'

                # Count country mentions in the text
                country_counts = self.count_country_mentions(text)

                

                # Group text by year
                self.grouped_texts_by_year[year] = self.grouped_texts_by_year.get(year, []) + [text]

                if country_counts:
                    # Get the top countries by mention count
                    top_countries = sorted(country_counts, key=country_counts.get, reverse=True)[:5]
                    self.grouped_texts_by_country_year[(top_countries[0], year)] = self.grouped_texts_by_country_year.get((top_countries[0], year), []) + [text]

            except Exception as e:
                print(f"Failed to process {filename}. Error: {e}")

            finally:
                # Clean up the temporary file
                if os.path.exists(temp_pdf_path):
                    os.remove(temp_pdf_path)

    def process_and_extract_keywords(self, years_to_process):
        """
            Process grouped texts and extract keywords for specified years.
        """

        combined_text = ""
        for (country, year), texts in self.grouped_texts_by_country_year.items():
            if year != "Unknown" and year not in years_to_process:
                continue
            
            print(f"\nProcessing keywords for {country} ({year})...")
            combined_text = " ".join(texts)
            
            
        final_keywords = self.keyword_processor.process_text(combined_text)
        
        return final_keywords

    def get_keywords_by_country_and_year(self):
        return self.grouped_texts_by_country_year

    def get_grouped_texts_by_year(self):
        """Get the dictionary of PDF texts indexed by filename."""
        return self.grouped_texts_by_year
