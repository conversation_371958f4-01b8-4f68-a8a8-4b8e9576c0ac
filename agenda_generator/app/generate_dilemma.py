import os
import json
from mq import connect_to_rabbitmq
from event_enum import event_dict
from lib.dilemma_reader import read_dilemmas_from_excel
from lib.keyword_matcher import match_keywords_with_dilemmas

current_dir = os.path.dirname(os.path.abspath(__file__))

excel_file_path = f"{current_dir}/assets/doc/dilemma.xlsx"



def generate_dilemmas(project, keywords, comments , articles, bluesky_posts, with_ontology): 
    # Process dilemmas
    queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
    try: 
        dilemmas_df = read_dilemmas_from_excel(excel_file_path)
        print("generate_dilemmas")
        matched_dilemmas_df = match_keywords_with_dilemmas(dilemmas_df, keywords, comments , articles, bluesky_posts)
        
        dilemmas_dict = matched_dilemmas_df.to_dict(orient="records") 
        
        
        payload = json.dumps({
        "event": event_dict['DILEMMA_RESULT'],
        "body": {
            "status": "completed",
            "project_id": project.get("id"),
            "result": {
                "type": "dilemma",
                "data": dilemmas_dict,
                "with_ontology": with_ontology
                    },
            
            
        }
        })
        
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
        print("Message sent")
    except Exception as e:
        print("Error in dilemma_processor", e)
        
        payload = json.dumps({
            "event": event_dict['FAILED_RESULT'],
            "body": {
                "type": "dilemma",
                "status": "failed",
                "project_id": project.get("id"),
                "message": "Failed to generate dilemmas",
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()

