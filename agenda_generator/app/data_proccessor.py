import os
import json
from concurrent.futures import ThreadPoolExecutor
import concurrent.futures
from mq import connect_to_rabbitmq
from lib.keyword_matcher import filter_keywords_with_user_keywords
from event_enum import event_dict
from utils.utils import get_keywords_from_pdf, get_keywords_from_twitter,  process_agenda_sources, fetch_all_data, get_year_range
from utils.utils import merge_keywords, get_keywords_from_news, get_keywords_from_youtube, get_keywords_from_bluesky
from lib.analyzer.ontolozy_analyzer import analyze_with_ontology
from lib.default_keywords import default_climate_keywords
from generate_ngram import generate_ngram
from generate_dilemma import generate_dilemmas

os.environ["TOKENIZERS_PARALLELISM"] = "false"


class DataSourceManager:
    """
    A class to manage and process different data sources for agenda generation
    """

    def __init__(self, max_workers=5):
        self.max_workers = max_workers
        self.results = self._initialize_results()

    def _initialize_results(self):
        """Initialize result containers for data processing"""
        return {
            'final_keywords': [],
            'pdf_final_keywords': [],
            'youtube_keywords': [],
            'bluesky_keywords': [],
            'twitter_keywords': [],
            'news_keywords': [],
            'comments': [],
            'bluesky_posts': [],
            'articles': [],
            'grouped_pdf_texts': {},
            'twitter_tweets': [],
            'with_ontology': False
        }

    def _detect_source_flags(self, social_media, printed_media, pdf):
        """Detect which sources need to be processed"""
        social_urls = set(url.lower() for url in social_media.get('urls', []))

        return {
            'process_pdf': pdf and len(pdf.get('attachments', [])) > 0,
            'process_youtube': any('youtube' in url for url in social_urls),
            'process_twitter':any('twitter' in url for url in social_urls),
            'process_bluesky': any('bluesky' in url for url in social_urls),
            'process_printed_media': len(printed_media.get('urls', [])) > 0
        }

    def _build_sources_list(self, source_flags):
        """Build list of sources to process"""
        return [source for source, flag in [
            ("youtube", source_flags['process_youtube']),
            ("twitter", source_flags['process_twitter']),
            ("bluesky", source_flags['process_bluesky']),
            ("printed_media", source_flags['process_printed_media'])
        ] if flag]

    def _submit_processing_tasks(self, executor, source_flags, all_data, pdf, target_country, years_to_process):
        """Submit all processing tasks to the executor"""
        futures = {}

        # Submit PDF processing task if needed
        if source_flags['process_pdf']:
            futures['pdf'] = executor.submit(
                get_keywords_from_pdf,
                pdf, target_country, years_to_process, default_climate_keywords
            )

        # Submit tasks for external data sources
        source_processors = {
            'youtube': (get_keywords_from_youtube, source_flags['process_youtube']),
            'twitter': (get_keywords_from_twitter, source_flags['process_twitter']),
            'bluesky': (get_keywords_from_bluesky, source_flags['process_bluesky']),
            'printed_media': (get_keywords_from_news, source_flags['process_printed_media'])
        }

        for source, (processor_func, should_process) in source_processors.items():
            if should_process and source in all_data and all_data[source]:
                futures[source] = executor.submit(
                    processor_func, all_data[source], default_climate_keywords
                )

        return futures

    def _process_task_results(self, futures):
        """Process results from completed tasks"""
        failed_sources = []

        for future in concurrent.futures.as_completed(futures.values()):
            source_name = None
            try:
                # Find which source this future belongs to
                for name, fut in futures.items():
                    if fut == future:
                        source_name = name
                        break

                result = future.result()

                # Process results based on source type
                if source_name == 'pdf':
                    self.results['pdf_final_keywords'], self.results['grouped_pdf_texts'] = result
                elif source_name == 'youtube':
                    self.results['youtube_keywords'], youtube_comments = result
                    self.results['comments'].extend(youtube_comments)
                elif source_name == 'twitter':
                    self.results['twitter_keywords'], tweets = result
                    self.results['twitter_tweets'].extend(tweets)
                elif source_name == 'bluesky':
                    self.results['bluesky_keywords'], posts = result
                    self.results['bluesky_posts'].extend(posts)
                elif source_name == 'printed_media':
                    self.results['news_keywords'], news_articles = result
                    self.results['articles'].extend(news_articles)

            except Exception as e:
                failed_sources.append(source_name)
                print(f"Error processing {source_name}: {e}")

        # Log failed sources for debugging
        if failed_sources:
            print(f"Failed to process sources: {failed_sources}")

    def process_sources(self, agenda_sources, target_country, target_start_year, target_end_year, keywords, use_ontology):
        """
        Manages the sources of the agenda with optimized processing
        """
        # Early validation and preprocessing
        
        # Reset results for new processing
        self.results = self._initialize_results()
        if not agenda_sources:
            return self.results 

        social_media, printed_media, pdf = process_agenda_sources(agenda_sources)
        years_to_process = get_year_range(target_start_year, target_end_year)
        keywords_array = [item.strip() for item in keywords.split(",")] if isinstance(keywords, str) else []

        

        # Detect which sources need processing
        source_flags = self._detect_source_flags(social_media, printed_media, pdf)

        # Build sources list
        sources = self._build_sources_list(source_flags)

        with_ontology = use_ontology
        
        # Early return if no sources to process
        if not sources and not source_flags['process_pdf']:
           return self.results

        # Fetch external data only if needed
        all_data = {}
        if sources:
            all_data = fetch_all_data(
                keywords=keywords_array,
                years=years_to_process,
                sources=sources,
                source_uri=printed_media.get('urls') if source_flags['process_printed_media'] else None
            )

        # Process data in parallel
        max_workers = min(len(sources) + (1 if source_flags['process_pdf'] else 0), self.max_workers)
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all processing tasks
            futures = self._submit_processing_tasks(
                executor, source_flags, all_data, pdf, target_country, years_to_process
            )

            # Process results as they complete
            self._process_task_results(futures)

        # Apply ontology analysis if requested
        
        
        if use_ontology and len(self.results['pdf_final_keywords']) > 0 :
            ontology_keywords = analyze_with_ontology(self.results['grouped_pdf_texts'], self.results['pdf_final_keywords'], target_start_year, target_end_year)
            
            if len(ontology_keywords) > 0:
                self.results['pdf_final_keywords'] = ontology_keywords
            else:
                self.results['with_ontology'] = False
        else:
            self.results['with_ontology'] = False
        
        # Merge keywords efficiently
        self.results['final_keywords'] = merge_keywords(
            self.results['pdf_final_keywords'],
            self.results['youtube_keywords'],
            self.results['news_keywords'],
            self.results['twitter_keywords'],
            self.results['bluesky_keywords']
        )

        return (self.results['final_keywords'], self.results['comments'],  self.results['articles'], self.results['twitter_tweets'],  self.results['grouped_pdf_texts'], self.results['bluesky_posts'], self.results['with_ontology'])


def process_data(body):
    project = body.get("project", {})
    # default_data_checkbox = body.get("default_data_checkbox", {})
    agenda_sources = body.get("agenda_sources", [])
    target_country = project.get("target_country")
    target_start_year = int(project.get("start_year"))
    target_end_year = int(project.get("end_year"))
    use_ontology = project.get("use_ontology")
    keywords = project.get("keywords")
    keywords_array =  [item.strip() for item in keywords.split(",")] if isinstance(keywords, str) else []
    print("ontology", use_ontology)
  
    # Use the class-based approach for cleaner code
    manager = DataSourceManager()
    final_keywords, comments , articles, twitter_tweets, grouped_pdf_texts, bluesky_posts, with_ontology = manager.process_sources(
        agenda_sources, target_country, target_start_year, target_end_year, keywords, use_ontology
    )
    
    filtered_final_keywords =  filter_keywords_with_user_keywords(final_keywords, keywords_array)
   

   
 
    
    if(len(filtered_final_keywords) == 0):
        payload = json.dumps({
            "event": event_dict['FAILED_RESULT'],
            "body": {
                "type": "dilemma",
                "status": "failed",
                "project_id": project.get("id"),
                "message": "No keywords found in the sources",
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
    else:     
        generate_ngram(project, comments, articles, twitter_tweets, grouped_pdf_texts, bluesky_posts)
        generate_dilemmas(project, filtered_final_keywords, comments , articles, bluesky_posts, with_ontology)
        
        
        
    
    