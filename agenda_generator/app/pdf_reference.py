import os
import json
import pandas as pd
from PyPDF2 import Pdf<PERSON>eader
import tempfile
import requests
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from mq import connect_to_rabbitmq
from event_enum import event_dict


def extract_text_from_pdf(pdf_path):
    text = ""
    with open(pdf_path, "rb") as file:
        reader = PdfReader(file)
        for page in reader.pages:
            text += page.extract_text() + " " if page.extract_text() else ""
    return text.strip()
        
        
        
        
def get_pdf_reference(body):
    project_id = body.get("project_id", {})
    
    matched_dilemmas = body.get("dilemmas", [])
    
    pdf = body.get("pdf", {})
    matched_dilemmas_df = pd.DataFrame(matched_dilemmas)
    
    # 🔄 Combine 'column_a' and 'column_b' to form dilemmas
    matched_dilemmas_df["dilemma"] = matched_dilemmas_df["column_a"] + " | " + matched_dilemmas_df["column_b"]

        # 📌 Function to extract text from PDFs
   
    
    
    if len(pdf.get('attachments', [])) > 0 :
        
        pdf_texts = {}
        
        for attachment in pdf.get('attachments', []):
            pdf_url = attachment["url"]  # Extract URL from the dictionary
            filename = attachment["filename"]
            
            print(f"\nProcessing {pdf_url}...")
            temp_pdf_path = None
            try:
                # Download the PDF to a temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_pdf:
                    response = requests.get(pdf_url, stream=True)
                    print(response)
                    if response.status_code == 200:
                        for chunk in response.iter_content(chunk_size=1024):
                            temp_pdf.write(chunk)
                    else:
                        print(f"Failed to download {filename}. HTTP Status Code: {response.status_code}")
                        continue

                    temp_pdf_path = temp_pdf.name

                    
                pdf_texts[filename] = extract_text_from_pdf(temp_pdf_path)
            except Exception as e:
                print(f"Failed to process {filename}. Error: {e.with_traceback}")

            finally:
                # Clean up the temporary file
                if os.path.exists(temp_pdf_path):
                    os.remove(temp_pdf_path)
        
        all_texts = matched_dilemmas_df["dilemma"].tolist() + list(pdf_texts.values())

        # ✍ Convert text to TF-IDF vectors
        vectorizer = TfidfVectorizer(stop_words="english")
        text_vectors = vectorizer.fit_transform(all_texts)

        # 🎯 Split vectors back into dilemmas and PDFs
        dilemma_vectors = text_vectors[: len(matched_dilemmas_df)]
        pdf_vectors = text_vectors[len(matched_dilemmas_df):]

        # 🔗 Compute cosine similarity between dilemmas and PDFs
        similarity_matrix = cosine_similarity(dilemma_vectors, pdf_vectors)

        # 📊 Assign each dilemma to the most relevant PDF
        grouped_results = []
        pdf_filenames = list(pdf_texts.keys())

        for i, dilemma in matched_dilemmas_df.iterrows():
            best_match_index = similarity_matrix[i].argmax()  # Get the index of the best-matching PDF
            best_match_pdf = pdf_filenames[best_match_index]
            
            grouped_results.append({"id": dilemma['id'], "metadata": {
               "reference": { "pdf": best_match_pdf}
            }})

        connection = connect_to_rabbitmq()
        channel = connection.channel()

        queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
        
        
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=json.dumps({
            "event": event_dict['PDF_REFERENCE_RESULT'],
            "body": {
                "project_id": project_id,
                "result": grouped_results
            }
        }))

        connection.close()
        print("Agenda Message sent")
   