import os
import json
from mq import connect_to_rabbitmq
from event_enum import event_dict
from lib.processors.n_gram_processor import NGramProcessor

def generate_ngram(project, comments, articles, twitter_tweets, grouped_pdf_texts, bluesky_posts):
    try: 
        n_gram_processor = NGramProcessor()
        n_gram_processor.create_text_dataframe(comments, articles, twitter_tweets, grouped_pdf_texts, bluesky_posts)
        n_gram_png = n_gram_processor.plot_ngram_frequency()
        
        payload = json.dumps({
            "event": event_dict['NGRAM_RESULT'],
            "body": {
                "project_id": project.get("id"),
                "result": {
                    "type": "n_gram",
                    "data": n_gram_png
                    },
            }
            })
        
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
    except Exception as e:
        print("Error in n_gram_processor", e)
        
        payload = json.dumps({
            "event": event_dict['FAILED_RESULT'],
            "body": {
                "type": "n_gram",
                "status": "failed",
                "project_id": project.get("id"),
                "message": "Failed to generate n-gram",
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
