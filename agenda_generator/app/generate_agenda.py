import os
import json
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from mq import connect_to_rabbitmq
from event_enum import event_dict
from lib.processors.agenda_processor import AgendaGenerator
from lib.keyword_matcher import  extract_columns_as_lists




def generate_agenda(body):
    queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
    try: 
        project_id = body.get("project_id", {})
        
        matched_dilemmas = body.get("dilemmas", [])
        matched_dilemmas_df = pd.DataFrame(matched_dilemmas)
    
        combined_list = extract_columns_as_lists(matched_dilemmas_df)
        
        
        
        agenda_generator = AgendaGenerator()

        
        clusters = agenda_generator.cluster_keywords(combined_list)
        print("agenda_generator clusters")
        
        raw_titles = []
        for cluster in clusters:
            
            raw_titles.append(agenda_generator.generate_agenda_title(cluster))

        filtered_titles = agenda_generator.filter_agenda_titles(raw_titles)

        
    # 🔄 Combine 'column_a' and 'column_b' to create a complete dilemma statement
        matched_dilemmas_df["dilemma"] = matched_dilemmas_df["column_a"] + " | " + matched_dilemmas_df["column_b"]
        print("matched_dilemmas_df")
        
        
        all_texts = matched_dilemmas_df["dilemma"].tolist() + filtered_titles

        vectorizer = TfidfVectorizer(stop_words="english")
        text_vectors = vectorizer.fit_transform(all_texts)
        print("vectorizer")


        # 🎯 Split vectors back into dilemmas and agendas
        dilemma_vectors = text_vectors[: len(matched_dilemmas_df)]
        agenda_vectors = text_vectors[len(matched_dilemmas_df):]
        print("agenda_vectors")
    
        # 🔗 Compute cosine similarity between dilemmas and agendas
        similarity_matrix = cosine_similarity(dilemma_vectors, agenda_vectors)
        print("similarity_matrix")




        # 📊 Assign each agenda to the most relevant dilemma
        agenda_to_dilemmas = {}

        # Iterate through the matched_dilemmas_df DataFrame
        for i, row in matched_dilemmas_df.iterrows():
            dilemma_id = row["id"]  # Get the dilemma number
            
            # Find the best-matching agenda for this dilemma
            best_match_index = similarity_matrix[i].argmax()  # Get the index of the best match
            best_match_agenda = filtered_titles[best_match_index]  # Get the agenda title
            
            # Add the dilemma_id to the list for this agenda
            if best_match_agenda in agenda_to_dilemmas:
                agenda_to_dilemmas[best_match_agenda].append(dilemma_id)
            else:
                agenda_to_dilemmas[best_match_agenda] = [dilemma_id]

        # Convert the dictionary to an array of objects (list of dictionaries)
        grouped_results = [
            {"title": agenda, "dilemma_ids": dilemma_ids}
            for agenda, dilemma_ids in agenda_to_dilemmas.items()
        ]
        
    
        connection = connect_to_rabbitmq()
        channel = connection.channel()

        
        
        
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=json.dumps({
            "event": event_dict['AGENDA_RESULT'],
            "body": {
                "status": "completed",
                "project_id": project_id,
                "result": 
                    {
                    "type": "agenda",
                    "data": grouped_results
                    }
            }
        }))
    
        connection.close()
        print("Agenda Message sent")
    except Exception as e:
        print("Error in agenda_processor", e)
        
        payload = json.dumps({
            "event": event_dict['FAILED_RESULT'],
            "body": {
                "type": "agenda",
                "status": "failed",
                "project_id": project_id,
                "message": "Failed to generate agenda",
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)
        connection.close()
        

