# Agenda Generation Service

## Overview
This project is a service for generating agendas and dilemmas based on various data sources, including PDFs, YouTube, Twitter, and news articles. It processes documents and social media to extract climate-related keywords, matches them with dilemmas, and outputs structured results for further use.

## Features
- Extracts keywords from PDFs, YouTube comments, Twitter, and news articles
- Matches extracted keywords with dilemmas from an Excel file
- Generates Agendas from dilemmas
- Integrates with RabbitMQ for event-driven communication

## Directory Structure
- `app/` - Main application code
  - `assets/` - Contains documents and CSVs used for processing
  - `lib/` - Core processing modules (PDF, YouTube, Twitter, News, Keyword, Dilemma)
  - `utils/` - Utility functions for keyword extraction and data merging
  - `data_proccessor.py` - Orchestrates data processing and aggregation
  - `main.py` - Entry point for running the service
- `requirements.txt` - Python dependencies
- `Dockerfile` and `docker-compose.yml` - Containerization and orchestration

## Setup Instructions
1. **Clone the repository**
2. **Install dependencies**:
   ```bash
   pip3 install -r requirements.txt
   ```
3. **Set up environment variables**:
   - Copy `.env.example` to `.env` and fill in required values:
     - `YOUTUBE_API_KEY`
     - `TWITTER_BEARER_TOKEN`
     - `NEWS_API_KEY`
     - `MQ_CLIMAS_ODOO_ROUTE_KEY`
     - Any other required variables
4. **Prepare assets**:
   - Place required PDF, Excel, and CSV files in `app/assets/doc/` and `app/assets/csv/` as needed.

## Running the Service
- **Locally**:
  ```bash
  python3 app/main.py
  ```
- **With Docker Compose**:
  ```bash
  docker-compose up --build
  ```

## Main Modules
- **PDFProcessor**: Handles PDF download, text extraction, and keyword processing
- **YouTubeProcessor**: Fetches comments and extracts keywords from YouTube videos
- **TwitterProcessor**: Fetches tweets and processes keywords
- **NewsProcessor**: Fetches news articles and extracts keywords
- **KeywordProcessor**: Performs keyword extraction and topic modeling
- **Dilemma Matching**: Matches extracted keywords with dilemmas from Excel

## Environment Variables
- `YOUTUBE_API_KEY`: API key for YouTube Data API
- `TWITTER_BEARER_TOKEN`: Bearer token for Twitter API
- `NEWS_API_KEY`: API key for News API
- `MQ_CLIMAS_ODOO_ROUTE_KEY`: RabbitMQ routing key

## Dependencies
See `requirements.txt` for a full list of Python dependencies.

