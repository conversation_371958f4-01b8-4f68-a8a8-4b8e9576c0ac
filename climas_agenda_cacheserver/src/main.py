from fastapi import FastAPI
import uvicorn
from presentation.routes.search_routes import router as search_router
from infrastructure.container import container
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
# Initialize FastAPI app
app = FastAPI(title="SocialCache Middleware")

@app.on_event("startup")
async def startup_event():
    # Initialize database through container
    container._init_db()

# Include routes
app.include_router(search_router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)