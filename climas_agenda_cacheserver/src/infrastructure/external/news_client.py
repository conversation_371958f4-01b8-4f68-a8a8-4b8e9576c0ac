import logging
import httpx
from typing import List, Dict, Any
from decouple import config
from domain.repositories.iexternal_client import IExternalClient
from exceptions import ValidationError, ExternalAPIError

class NewsClient(IExternalClient):
    def __init__(self, api_key: str = None):
        self.api_key = api_key or config("NEWS_API_KEY")
        if not self.api_key:
            raise ValidationError("NewsAPI.ai key not provided.")

    async def fetch_data(self, keyword: str, **kwargs) -> List[Dict[str, Any]]:
        try:
            source = kwargs.get('source')
            year = kwargs.get('year')
            
            max_results = kwargs.get('max_results', 100)

            if not keyword:
                raise ValidationError("Keyword must be provided")

            url = "https://eventregistry.org/api/v1/article/getArticles"
            headers = {"Content-Type": "application/json"}
            
            start_date = f"{year}-01-01" if year else None
            end_date = f"{year}-12-31" if year else None
            params = self._build_params(keyword, max_results, source, start_date, end_date)

            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=params)
                response.raise_for_status()
                data = response.json()

                if not self.validate_response(data):
                    raise ExternalAPIError("NewsAPI", "Invalid response format")

                articles_data = data.get("articles", {}).get("results", [])
                if not articles_data:
                    logging.info(f"No articles found for keyword: {keyword}")
                    return []
                
                return self._normalize_articles(articles_data, keyword)

        except httpx.HTTPError as e:
            logging.error(f"NewsAPI HTTP error: {str(e)}")
            raise ExternalAPIError("NewsAPI", f"HTTP error: {str(e)}")
        except Exception as e:
            logging.error(f"NewsAPI error: {str(e)}")
            raise ExternalAPIError("NewsAPI", str(e))

    def validate_response(self, response: Any) -> bool:
        return isinstance(response, dict)

    def _build_params(self, keyword: str, max_results: int, source: str = None,
                      start_date: str = None, end_date: str = None) -> Dict:
        params = {
            "action": "getArticles",
            "keyword": keyword,
            "lang": "eng",
            "articlesCount": min(max_results, 100),
            "articlesSortBy": "date",
            "articlesSortByAsc": False,
            "resultType": "articles",
            "dataType": ["news", "blog"],
            "apiKey": self.api_key
        }

        if start_date:
            params["dateStart"] = start_date
        if end_date:
            params["dateEnd"] = end_date
        if source:
            params["sourceUri"] = source
        return params

    def _normalize_articles(self, articles: List[Dict], keyword: str) -> List[Dict]:
        return [{
            "keyword": keyword,
            "uid": article.get("uri", ""),
            "title": article.get("title", ""),
            "url": article.get("url", ""),
            "published_at": article.get("dateTimePub", ""),
            "description": article.get("body", ""),
            "sourceUri": article.get("source", {}).get("uri", "")
        } for article in articles]
    