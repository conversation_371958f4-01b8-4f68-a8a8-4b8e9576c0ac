import logging
import googleapiclient.discovery
from typing import List, Dict, Any
from decouple import config
from domain.repositories.iexternal_client import IExternalClient
from exceptions import ValidationError, ExternalAPIError

class YouTubeClient(IExternalClient):
    def __init__(self, api_key: str = None):
        self.api_key = api_key or config("YOUTUBE_API_KEY")
        if not self.api_key:
            raise ValidationError("YouTube API key not provided.")
        self.youtube = googleapiclient.discovery.build("youtube", "v3", developerKey=self.api_key)

    async def fetch_data(self, keyword: str, start_year: int, end_year: int) -> List[Dict[str, Any]]:
        try:
            start_year = start_year
            end_year = end_year if end_year else start_year
            max_results = 100

            if not start_year:
                raise ValidationError("start_year is required")

            
            start_date = f"{start_year}-01-01T00:00:00Z"
            end_date = f"{end_year}-12-31T23:59:59Z"

            search_response = self.youtube.search().list(
                q=keyword,
                part="id,snippet",
                maxResults=5,
                type="video",
                publishedAfter=start_date,
                publishedBefore=end_date
            ).execute()

            if not self.validate_response(search_response):
                raise ExternalAPIError("YouTube", "Invalid response format")

            return self._process_videos(search_response["items"], max_results, keyword)

        except Exception as e:
            logging.error(f"YouTube API error: {str(e)}")
            raise ExternalAPIError("YouTube", str(e))

    def validate_response(self, response: Any) -> bool:
        return isinstance(response, dict) and "items" in response

    def _process_videos(self, videos: List[Dict], max_results: int, keyword: str) -> List[Dict]:
        processed_videos = []
        all_comments = []

        for video in videos:
            try:
                video_data = self._extract_video_data(video, keyword)
                comments = self._fetch_video_comments(video["id"]["videoId"], max_results)
                video_data["comments"] = comments
                processed_videos.append(video_data)
                all_comments.extend(comments)

                if len(all_comments) >= max_results:
                    break

            except Exception as e:
                logging.warning(f"Error processing video {video.get('id', {}).get('videoId')}: {e}")
                continue

        return processed_videos

    def _extract_video_data(self, video: Dict, keyword: str) -> Dict:
        video_id = video["id"]["videoId"]
        return {
            "keyword": keyword,
            "uid": video_id,
            "title": video["snippet"]["title"],
            "video_url": f"https://www.youtube.com/watch?v={video_id}",
            "published_at": video["snippet"]["publishedAt"],
            "description": video["snippet"]["description"]
        }

    def _fetch_video_comments(self, video_id: str, max_results: int) -> List[Dict]:
        comments = []
        try:
            comment_response = self.youtube.commentThreads().list(
                part="snippet",
                videoId=video_id,
                textFormat="plainText",
                maxResults=min(100, max_results)
            ).execute()

            while comment_response:
                for item in comment_response["items"]:
                    comment = item["snippet"]["topLevelComment"]["snippet"]
                    comments.append({
                        "comment": comment["textDisplay"],
                        "published_at": comment["publishedAt"]
                    })
                    if len(comments) >= max_results:
                        return comments

                if "nextPageToken" in comment_response and len(comments) < max_results:
                    comment_response = self.youtube.commentThreads().list(
                        part="snippet",
                        videoId=video_id,
                        textFormat="plainText",
                        maxResults=min(100, max_results - len(comments)),
                        pageToken=comment_response["nextPageToken"]
                    ).execute()
                else:
                    break

        except Exception as e:
            logging.warning(f"Error fetching comments for video {video_id}: {e}")

        return comments