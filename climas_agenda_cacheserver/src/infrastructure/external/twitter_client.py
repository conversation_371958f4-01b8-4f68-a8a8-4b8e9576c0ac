import logging
from math import log
import httpx
from typing import List, Dict, Any
from decouple import config
from domain.repositories.iexternal_client import IExternalClient
from exceptions import ValidationError, ExternalAPIError

class TwitterClient(IExternalClient):
    def __init__(self, api_key: str = None):
        self.bearer_token = api_key or config("TWITTER_BEARER_TOKEN")
        if not self.bearer_token:
            raise ValidationError("Twitter Bearer token not provided.")

    async def fetch_data(self, keyword: str, **kwargs) -> List[Dict[str, Any]]:
        try:
            max_results = kwargs.get('max_results', 100)
            

            url = "https://api.twitter.com/2/tweets/search/recent"
            headers = {"Authorization": f"Bearer {self.bearer_token}"}
            params = {
                "query": keyword,
                "max_results": min(max_results, 100),
                "tweet.fields": "created_at",
            }

            all_tweets = []
            async with httpx.AsyncClient() as client:
                while len(all_tweets) < max_results:
                    response = await client.get(url, headers=headers, params=params)
                    response.raise_for_status()
                    data = response.json()

                    if not self.validate_response(data):
                        raise ExternalAPIError("Twitter", "Invalid response format")

                    tweets = data.get("data", [])
                    all_tweets.extend(tweets)

                    next_token = data.get("meta", {}).get("next_token")
                    if not next_token:
                        break
                    params["next_token"] = next_token

            return self._normalize_tweets(all_tweets, keyword)

        except httpx.HTTPError as e:
            logging.error(f"Twitter API HTTP error: {str(e)}")
            raise ExternalAPIError("Twitter", f"HTTP error: {str(e)}")
        except Exception as e:
            logging.error(f"Twitter API error: {str(e)}")
            raise ExternalAPIError("Twitter", str(e))

    def validate_response(self, response: Any) -> bool:
        return isinstance(response, dict) and ("data" in response or "meta" in response)


    def _normalize_tweets(self, tweets: List[Dict], keyword: str) -> List[Dict]:
        logging.info(f"Normalizing {len(tweets)} tweets for keyword: {keyword}")
        return [{
            "keyword": keyword,
            "uid": tweet["id"],
            "text": tweet["text"],
            "published_at": tweet["created_at"]
        } for tweet in tweets]