import logging

import logging
from typing import List, Dict, Any
from decouple import config

from domain.repositories.iexternal_client import IExternalClient
from exceptions import ValidationError, ExternalAPIError

from atproto import Client
from datetime import datetime

class BlueskyClient(IExternalClient):
    def __init__(self):
        self.handle = config("BLUESKY_HANDLE")
        self.password = config("BLUESKY_PASSWORD")
        if not self.handle or not self.password:
            raise ValidationError("Bluesky handle or password not provided.")
        self.client = Client()
        try:
            self.client.login(self.handle, self.password)
        except Exception as e:
            raise ExternalAPIError("Bluesky", f"Login failed: {e}")

    async def fetch_data(self, keyword: str, start_year: int, end_year: int) -> List[Dict[str, Any]]:
        try:
            if not keyword:
                raise ValidationError("Keyword is required for Bluesky search.")

            # Bluesky API does not support date range filtering directly in search.
            # We will fetch posts and then filter by date.
            # Max limit for search_posts is typically 100.
            response = self.client.app.bsky.feed.search_posts({'q': keyword, 'limit': 100})
            
      
            processed_posts = self._process_posts(response.posts, keyword, start_year, end_year)
            return processed_posts

        except Exception as e:
            logging.error(f"Bluesky API error: {str(e)}")
            raise ExternalAPIError("Bluesky", str(e))

    def validate_response(self, response: Any) -> bool:
        return isinstance(response, dict) and "posts" in response

    def _process_posts(self, posts: List[Any], keyword: str, start_year: int, end_year: int) -> List[Dict]:
        processed_data = []
        for post in posts:
            try:
                created_at_str = post.record.created_at
                processed_data.append({
                    "keyword": keyword,
                    "uid": post.uri.split('/')[-1],
                    "text": post.record.text,
                    "author": post.author.handle,
                    "published_at": created_at_str,
                    "url": f"https://bsky.app/profile/{post.author.handle}/post/{post.uri.split('/')[-1]}"
                })
            except Exception as e:
                logging.warning(f"Error processing Bluesky post {post.uri}: {e}")
                continue
        return processed_data
