import logging
import pymongo
from typing import Any

async def ensure_indexes(db: Any) -> None:
    """Create indexes for MongoDB collections to optimize query performance."""
    try:
        # Create indexes for YouTube collection
        await db.blusky.create_indexes([
            pymongo.IndexModel([('uid', pymongo.ASCENDING)], unique=True),
            pymongo.IndexModel([('published_at', pymongo.ASCENDING)]),
            pymongo.IndexModel([('keyword', pymongo.DESCENDING)]),
        ])
        
        # Create indexes for Twitter collection
        await db.twitter.create_indexes([
            pymongo.IndexModel([('uid', pymongo.ASCENDING)], unique=True),
            pymongo.IndexModel([('keyword', pymongo.DESCENDING)]),
            pymongo.IndexModel([('published_at', pymongo.ASCENDING)])
        ])
        
        # Create indexes for keyword_search collection
        await db.keyword_search.create_indexes([
            pymongo.IndexModel([('keyword', pymongo.ASCENDING)], unique=True)
        ])
        
        # Create indexes for News collection
        await db.newsapi.create_indexes([
            pymongo.IndexModel([('uid', pymongo.ASCENDING)], unique=True),
            pymongo.IndexModel([('keyword', pymongo.DESCENDING)]),
            pymongo.IndexModel([('published_at', pymongo.DESCENDING)])
        ])
        
         # Create indexes for YouTube collection
        await db.youtube.create_indexes([
            pymongo.IndexModel([('uid', pymongo.ASCENDING)], unique=True),
            pymongo.IndexModel([('published_at', pymongo.ASCENDING)]),
            pymongo.IndexModel([('keyword', pymongo.DESCENDING)]),
        ])
        
        logging.info('Database indexes created successfully')
    except Exception as e:
        logging.error(f'Error creating indexes: {str(e)}')
        raise