import logging
from typing import Dict, Any, List
from domain.repositories.isearch_repository import ISearchRepository
from domain.entities.search_entity import SearchEntity
from infrastructure.external.youtube_client import YouTubeClient
from infrastructure.external.twitter_client import Twitter<PERSON>lient
from infrastructure.external.news_client import News<PERSON>lient
from infrastructure.external.blusky_client import Bluesky<PERSON>lient
from exceptions import DatabaseError, ExternalAPIError, ValidationError
import pymongo
from decouple import config

class MongoSearchRepository(ISearchRepository):
    def __init__(
        self,
        db,
    ):
        self.twitter_client = TwitterClient()
        self.news_client = NewsClient()
        self.youtube_client = YouTubeClient()
        self.bluesky_client = BlueskyClient()
        self.db = db

    async def find_existing_search(self, keyword: str) -> Dict[str, Any]:
        return await self.db["keyword_search"].find_one({"keyword": keyword})

    async def save_search(self, search_entry: Dict[str, Any]) -> None:
        try:
            await self.db.get_collection("keyword_search").insert_one(search_entry)
        except Exception as e:
            raise DatabaseError(f"Failed to save search entry", {"error": str(e)})

    async def update_search(
        self,
        keyword: str,
        sources: List[str],
        source_date_ranges: Dict[str, Any],
        source_uri: List[str],
    ) -> None:
        try:
            update_ops = {
                "$addToSet": {
                    "data_source": {"$each": sources},
                    "source_uri": {"$each": source_uri},
                }
            }

            # Update date ranges for each source
            for source in sources:
                if source in source_date_ranges and source_date_ranges[source]:
                    update_ops["$addToSet"][f"source_date_ranges.{source}"] = {
                        "$each": source_date_ranges[source]
                    }

            await self.db["keyword_search"].update_one({"keyword": keyword}, update_ops)
        except Exception as e:
            raise DatabaseError(f"Failed to update search entry", {"error": str(e)})

    async def fetch_from_collections(
        self,
        keyword: str,
        sources: List[str],
        years: List[int],
        source_uri: List[str],
        total_n_keywords: int,
    ) -> Dict[str, Any]:
        try:
            results = {}
            # Get the existing search to check source-specific date ranges
            existing_search = await self.find_existing_search(keyword)
            if not existing_search:
                return results

            # Filter years based on source-specific availability
            source_date_ranges = existing_search.get("source_date_ranges", {})

            for source in sources:
                available_years = source_date_ranges.get(source, [])
                valid_years = [year for year in years if year in available_years]

                if not valid_years:
                    continue

                year_queries = [
                    {"published_at": {"$gte": f"{year}-01-01", "$lte": f"{year}-12-31"}}
                    for year in valid_years
                ]

                data_query = {"$and": [{"$or": year_queries}, {"keyword": keyword}]}

            await self._fetch_source_data(
                sources, data_query, source_uri, results, total_n_keywords
            )

            return results
        except Exception as e:
            raise DatabaseError("Error fetching from collections", {"error": str(e)})

    async def fetch_from_external(
        self, keyword: str, source: str, year: int, source_uri: List[str]
    ) -> Dict[str, Any]:
        results = {}
        
        # Process each source independently
        if source == "bluesky":
            await self._fetch_bluesky_data(keyword, year, results)
            
        elif source == "youtube":
            await self._fetch_youtube_data(keyword, year, results)

        elif source == "twitter":
            await self._fetch_twitter_data(keyword, results)

        elif source == "printed_media":
            if config("IS_NEWS_API_DATE") == "true":
                await self._fetch_news_data(keyword, source_uri, results, year)
            else:
                await self._fetch_news_data(keyword, source_uri, results)

        return results

    async def _fetch_source_data(
        self,
        sources: List[str],
        data_query: Dict,
        source_uri: List[str],
        results: Dict,
        total_n_keywords: int,
    ) -> None:
        # Calculate document limit based on number of keywords
        news_doc_limit = int(100 / total_n_keywords) if total_n_keywords <= 5 else 20
        youtube_doc_limit = int(200 / total_n_keywords) if total_n_keywords <= 5 else 50
        twitter_doc_limit = int(200 / total_n_keywords) if total_n_keywords <= 5 else 50
        bluesky_doc_limit = int(200 / total_n_keywords) if total_n_keywords <= 5 else 50

        if "youtube" in sources:
            youtube_docs = (
                await self.db["youtube"]
                .find(data_query)
                .limit(youtube_doc_limit)
                .to_list(None)
            )
            results["youtube"] = self._normalize_youtube_data(youtube_docs)

        if "twitter" in sources:
            twitter_docs = (
                await self.db["twitter"]
                .find({"keyword": data_query["$and"][1]["keyword"]})
                .sort([("published_at", -1)])
                .limit(twitter_doc_limit)
                .to_list(None)
            )
            results["twitter"] = twitter_docs

        if "bluesky" in sources:
            bluesky_docs = (
                await self.db["bluesky"]
                .find({"keyword": data_query["$and"][1]["keyword"]})
                .sort([("published_at", -1)])
                .limit(bluesky_doc_limit)
                .to_list(None)
            )
            results["bluesky"] = bluesky_docs

        if "printed_media" in sources:
            if source_uri:
                data_query["$and"].append({"sourceUri": {"$in": source_uri}})
            news_docs = (
                await self.db["newsapi"]
                .find(data_query)
                .limit(news_doc_limit)
                .to_list(None)
            )
            results["printed_media"] = news_docs

    async def _fetch_youtube_data(self, keyword: str, year: int, results: Dict) -> None:
        try:
            youtube_data = await self.youtube_client.fetch_data(keyword, year, year)
            await self.db.get_collection("youtube").insert_many(
                youtube_data, ordered=False
            )
            logging.info(f"Fetched {len(youtube_data)} youtube video for keyword: {keyword}")
            results["youtube"] = self._normalize_youtube_data(youtube_data)
        except pymongo.errors.BulkWriteError as e:
            logging.info(
                f"Skipped {len(e.details.get('writeErrors', []))} duplicate YouTube entries"
            )
        except Exception as e:
            raise ExternalAPIError("YouTube", str(e))

    async def _fetch_twitter_data(self, keyword: str, results: Dict) -> None:
        try:
            twitter_data = await self.twitter_client.fetch_data(keyword)
            await self.db.get_collection("twitter").insert_many(
                twitter_data, ordered=False
            )
            logging.info(f"Fetched {len(twitter_data)} tweets for keyword: {keyword}")
            results["twitter"] = twitter_data
        except pymongo.errors.BulkWriteError as e:
            logging.info(
                f"Skipped {len(e.details.get('writeErrors', []))} duplicate Twitter entries"
            )
        except Exception as e:
            raise ExternalAPIError("Twitter", str(e))
            
    async def _fetch_bluesky_data(self, keyword: str, year: int, results: Dict) -> None:
        try:
            bluesky_data = await self.bluesky_client.fetch_data(keyword, year, year)
            if bluesky_data:
                # Save to database
                await self.db.get_collection("bluesky").insert_many(
                    bluesky_data, ordered=False
                )
                
                # Add to results
                results["bluesky"] = bluesky_data
                        
            logging.info(f"Fetched {len(bluesky_data)} Bluesky posts for keyword: {keyword}")
        except pymongo.errors.BulkWriteError as e:
            logging.info(
                f"Skipped {len(e.details.get('writeErrors', []))} duplicate Bluesky entries"
            )
        except Exception as e:
            raise ExternalAPIError("Bluesky", str(e))

    async def _fetch_news_data(
        self, keyword: List[str], source_uri: List[str], results: Dict,
        year: int | None = None) -> None:
        try:
            if not source_uri:
                raise ValidationError("source_uri is required for printed_media")

            news_data = await self.news_client.fetch_data(
                keyword, source=source_uri if source_uri else None, year=year
            )

            if news_data:
                await self.db.get_collection("newsapi").insert_many(
                    news_data, ordered=False
                )
            logging.info(f"Fetched {len(news_data)} news articles for keyword: {keyword}")
            results["printed_media"] = news_data
        except pymongo.errors.BulkWriteError as e:
            logging.info(
                f"Skipped {len(e.details.get('writeErrors', []))} duplicate News entries"
            )
        except Exception as e:
            raise ExternalAPIError("News API", str(e))

    def _normalize_youtube_data(self, videos: List[Dict]) -> List[Dict]:
        all_comments = []
        for video in videos:
            for comment in video.get("comments", []):
                all_comments.append(
                    {
                        "comment": comment.get("comment"),
                        "video_url": video.get("video_url"),
                        "published_at": comment.get("published_at"),
                    }
                )
        return all_comments[:50] if all_comments else []
