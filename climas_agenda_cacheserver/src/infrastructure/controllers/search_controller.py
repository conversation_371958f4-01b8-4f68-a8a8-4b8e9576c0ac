from typing import Dict, Any
from domain.entities.search_entity import SearchEntity
from application.use_cases.search_use_case import SearchUseCase
from exceptions import ValidationError, DatabaseError, ExternalAPIError
import logging
from bson import ObjectId

class SearchController:
    def __init__(self, search_use_case: SearchUseCase):
        self.search_use_case = search_use_case

    async def handle_search(self, request) -> Dict[str, Any]:
        """Handle search request and return results"""
        try:
            # Validate sources
            valid_sources = ["youtube", "twitter", "printed_media", "bluesky"]
            if hasattr(request, 'sources'):
                for source in request.sources:
                    if source not in valid_sources:
                        raise ValidationError(f"Invalid source: {source}. Valid sources are: {', '.join(valid_sources)}")
            
            # Create search entity from request
            search_entity = SearchEntity(
                keywords=request.keywords,
                sources=request.sources,
                years=request.years,
                source_uri=request.source_uri if hasattr(request, 'source_uri') else None
            )

            # Execute search use case
            results = await self.search_use_case.execute(search_entity)
            return self._serialize_results(results)

        except (ValidationError, DatabaseError, ExternalAPIError) as e:
            raise e
        except Exception as e:
            logging.error(f"Unexpected error during search: {str(e)}")
            raise Exception("Unexpected error during search", {"error": str(e)})

    def _serialize_results(self, obj: Any) -> Any:
        """Serialize results for response"""
        if isinstance(obj, ObjectId):
            return str(obj)
        if hasattr(obj, 'to_dict'):
            return obj.to_dict()
        if isinstance(obj, dict):
            return {k: self._serialize_results(v) for k, v in obj.items()}
        if isinstance(obj, list):
            return [self._serialize_results(item) for item in obj]
        return obj