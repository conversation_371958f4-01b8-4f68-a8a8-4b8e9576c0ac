from typing import Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient
from decouple import config
from infrastructure.repositories.search_repository import MongoSearchRepository
from application.use_cases.search_use_case import SearchUseCase
from infrastructure.controllers.search_controller import SearchController
from infrastructure.repositories.mongo_indexes import ensure_indexes
import asyncio
class Container:
    """Dependency Injection Container"""
    
    def __init__(self):
        self._instances: Dict[str, Any] = {}
        self._init_db()
        # Ensure indexes are created
        asyncio.create_task(self._create_indexes())

    def _init_db(self) -> None:
        """Initialize database connection"""
        mongo_uri = config("MONGO_URI", default="mongodb://mongodb:27017/")
        client = AsyncIOMotorClient(mongo_uri)
        self._instances['db'] = client[ config("MONGODB")]

    async def _create_indexes(self) -> None:
        await ensure_indexes(self._instances['db'])


    def get_search_repository(self) -> MongoSearchRepository:
        """Get search repository instance"""
        if 'search_repository' not in self._instances:
            self._instances['search_repository'] = MongoSearchRepository(self._instances['db'])
        return self._instances['search_repository']

    def get_search_use_case(self) -> SearchUseCase:
        """Get search use case instance"""
        if 'search_use_case' not in self._instances:
            repository = self.get_search_repository()
            self._instances['search_use_case'] = SearchUseCase(repository)
        return self._instances['search_use_case']

    def get_search_controller(self) -> SearchController:
        """Get search controller instance"""
        if 'search_controller' not in self._instances:
            use_case = self.get_search_use_case()
            self._instances['search_controller'] = SearchController(use_case)
        return self._instances['search_controller']

# Create global container instance
container = Container()