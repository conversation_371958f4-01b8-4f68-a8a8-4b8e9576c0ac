from abc import ABC, abstractmethod
from typing import List, Dict, Any

class IExternalClient(ABC):
    """Abstract base class for external API clients"""

    @abstractmethod
    def __init__(self, api_key: str):
        """Initialize client with API key"""
        pass

    @abstractmethod
    async def fetch_data(self, keywords: List[str], **kwargs) -> List[Dict[str, Any]]:
        """Fetch data from external API"""
        pass

    @abstractmethod
    def validate_response(self, response: Any) -> bool:
        """Validate API response"""
        pass