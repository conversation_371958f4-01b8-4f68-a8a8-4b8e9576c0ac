from abc import ABC, abstractmethod
from typing import Dict, Any, List

class ISearchRepository(ABC):
    """Abstract base class for search operations"""
    
    @abstractmethod
    async def find_existing_search(self, keyword: str) -> Dict[str, Any]:
        """Find existing search by keyword"""
        pass

    @abstractmethod
    async def save_search(self, search_entry: Dict[str, Any]) -> None:
        """Save new search entry"""
        pass

    @abstractmethod
    async def update_search(self, keyword: str, sources: List[str], source_date_ranges: Dict[str, Any], source_uri: List[str]) -> None:
        """Update existing search entry"""
        pass

    @abstractmethod
    async def fetch_from_collections(self, keywords: List[str], sources: List[str], years: List[int], source_uri: List[str]) -> Dict[str, Any]:
        """Fetch data from collections"""
        pass

    @abstractmethod
    async def fetch_from_external(self, keyword:str, source: str, year: int, source_uri: List[str]) -> Dict[str, Any]:
        """Fetch data from external sources"""
        pass