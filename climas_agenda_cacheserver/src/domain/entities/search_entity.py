from dataclasses import dataclass
from typing import List

@dataclass
class SearchEntity:
    keywords: List[str]
    sources: List[str]
    years: List[int]
    source_uri: List[str] = None

    def validate(self) -> bool:
        """Validate search parameters"""
        if not self.keywords or not self.sources or not self.years:
            return False
        if not isinstance(self.years, list) or len(self.years) < 1:
            return False
        return True

    def get_source_uri(self) -> List[str]:
        """Get source URI with default handling"""
        return self.source_uri if self.source_uri else []