from typing import Any, Dict, Optional

class SocialCacheException(Exception):
    """Base exception class for SocialCache application"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)

class DatabaseError(SocialCacheException):
    """Raised when database operations fail"""
    pass

class ExternalAPIError(SocialCacheException):
    """Raised when external API calls fail"""
    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        self.service = service
        super().__init__(f"{service} API Error: {message}", details)

class ValidationError(SocialCacheException):
    """Raised when input validation fails"""
    pass

class ResourceNotFoundError(SocialCacheException):
    """Raised when a requested resource is not found"""
    pass