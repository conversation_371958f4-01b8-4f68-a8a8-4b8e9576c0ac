from typing import Dict, Any, List
from domain.entities.search_entity import SearchEntity
from domain.repositories.isearch_repository import ISearchRepository
from exceptions import ValidationError, ExternalAPIError
import logging


class SearchUseCase:
    def __init__(self, search_repository: ISearchRepository):
        self.search_repository = search_repository

    async def execute(self, search_entity: SearchEntity) -> Dict[str, Any]:
        """Execute search operation with given parameters"""
        try:
            if not search_entity.validate():
                raise ValidationError("Invalid search parameters")

            final_results = {}
            for keyword in search_entity.keywords:
                logging.info(f"for keyword: {keyword}")
                keyword_results = await self._process_keyword(
                    keyword,
                    search_entity.sources,
                    search_entity.years,
                    search_entity.get_source_uri(),
                    len(search_entity.keywords),
                )
                self._merge_results(final_results, keyword_results)

            return final_results

        except Exception as e:
            logging.error(f"Error in search use case: {str(e)}")
            raise

    async def _process_keyword(
        self,
        keyword: str,
        sources: List[str],
        years: List[int],
        source_uri: List[str],
        total_n_keywords: int,
    ) -> Dict[str, Any]:
        """Process individual keyword search"""
        existing_search = await self.search_repository.find_existing_search(keyword)
        logging.info(f"_process_keyword: {keyword}")
        if existing_search:
            return await self._handle_existing_search(
                existing_search, keyword, sources, years, source_uri, total_n_keywords
            )
        else:
            logging.info(f"_handle_new_search")

            return await self._handle_new_search(
                keyword, sources, years, source_uri, total_n_keywords
            )

    async def _handle_existing_search(
        self,
        existing_search: Dict,
        keyword: str,
        sources: List[str],
        years: List[int],
        source_uri: List[str],
        total_n_keywords: int,
    ) -> Dict[str, Any]:
        """Handle search for existing keyword with source-specific date ranges"""
        source_date_ranges = existing_search.get("source_date_ranges", {})
        missing_sources = [
            s for s in sources if s not in existing_search["data_source"]
        ]
        missing_uris = []
        if "printed_media" in sources and source_uri:
            existing_uris = existing_search.get("source_uri", [])
            missing_uris = [uri for uri in source_uri if uri not in existing_uris]

        # Handle existing sources with missing years
        for source in sources:
            if source in missing_sources:
                continue

            available_years = source_date_ranges.get(source, [])
            missing_years = [y for y in years if y not in available_years]

            fetched_years = []
            if missing_years:
                logging.info(f"_handle_existing_search: keyword {keyword}  missing_years:{missing_years}")
                for year in missing_years:
                    try:
                        await self.search_repository.fetch_from_external(
                            keyword, source, year, source_uri
                        )
                        fetched_years.append(year)
                    except (ExternalAPIError, Exception) as e:
                        logging.error(
                            f"Failed to fetch {source} data for year {year}: {str(e)}"
                        )
                available_years.extend(fetched_years)
                source_date_ranges[source] = available_years
        await self.search_repository.update_search(
            keyword, sources, source_date_ranges, source_uri
        )

        # Handle completely new sources
        if missing_sources:
            logging.info(f"_handle_existing_search:  missing_sources:{missing_sources}")
            for source in missing_sources:
                fetched_years = []
                for year in years:
                    try:
                        await self.search_repository.fetch_from_external(
                            keyword, source, year, source_uri
                        )
                        fetched_years.append(year)
                    except (ExternalAPIError, Exception) as e:
                        logging.error(
                            f"Failed to fetch {source} data for year {year}: {str(e)}"
                        )
                source_date_ranges[source] = fetched_years

        await self.search_repository.update_search(
            keyword, missing_sources, source_date_ranges, source_uri
        )

        # Handle missing URIs for printed media
        if missing_uris and "printed_media" in sources:
            logging.info(f"_handle_existing_search:  missing_uris:{missing_uris}")
            fetched_years = []
            for year in years:
                try:
                    await self.search_repository.fetch_from_external(
                        keyword, "printed_media", year, missing_uris
                    )
                    fetched_years.append(year)
                except (ExternalAPIError, Exception) as e:
                    logging.error(
                        f"Failed to fetch printed_media data for year {year}: {str(e)}"
                    )

            # Merge existing and newly fetched years for printed media
            existing_years = source_date_ranges.get("printed_media", [])
            source_date_ranges["printed_media"] = list(set(existing_years + fetched_years))
            await self.search_repository.update_search(
                keyword, ["printed_media"], source_date_ranges, missing_uris
            )

        return await self.search_repository.fetch_from_collections(
            keyword, sources, years, source_uri, total_n_keywords
        )

    async def _handle_new_search(
        self,
        keyword: str,
        sources: List[str],
        years: List[int],
        source_uri: List[str],
        total_n_keywords: int,
    ) -> Dict[str, Any]:
        """Handle search for new keyword with source-specific date ranges"""

        source_date_ranges = {}
        for source in sources:
            source_years = []
            for year in years:
                try:
                    await self.search_repository.fetch_from_external(
                        keyword, source, year, source_uri
                    )

                    source_years.append(year)

                except Exception as e:
                    logging.error(
                        f"Failed to fetch {source} data for year {year}: {str(e)}"
                    )
            source_date_ranges[source] = source_years

        logging.info(
            f"_handle_new_search: source_date_ranges: {source_date_ranges} successful_sources:{sources}"
        )
        search_entry = {
            "keyword": keyword,
            "data_source": sources,
            "source_date_ranges": source_date_ranges,
            "source_uri": source_uri,
        }
        await self.search_repository.save_search(search_entry)
        return await self.search_repository.fetch_from_collections(
            keyword, sources, years, source_uri, total_n_keywords
        )

    def _merge_results(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """Merge search results"""
        for source_key, data in source.items():
            if source_key not in target:
                target[source_key] = []
            target[source_key].extend(data)
