from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, validator
from typing import List
import logging
from infrastructure.container import container

class SearchRequest(BaseModel):
    keywords: List[str]
    sources: List[str]
    years: List[int]
    source_uri: List[str] = []  # Optional list of news source URIs to filter by

    @validator('source_uri')
    def validate_source_uri(cls, v, values):
        if 'sources' in values and 'printed_media' in values['sources'] and not v:
            raise ValueError('source_uri is required when printed_media is selected as a source')
        return v

# Create versioned router
router = APIRouter(prefix="/api/v1")

@router.post("/search")
async def handle_search(request: SearchRequest):
    try:
        logging.info(f"Search request received - keywords: {request.keywords}, sources: {request.sources}, years: {request.years}")
        
        controller = container.get_search_controller()
        results = await controller.handle_search(request)
        
        logging.info("Search completed successfully")
        return {"results": results}
    except Exception as e:
        logging.error(f"Search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.get("/health")
def health_check():
    return {"status": "healthy", "version": "1.0.0"}