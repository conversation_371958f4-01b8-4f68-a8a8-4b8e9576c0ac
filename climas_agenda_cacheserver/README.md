# Social Media Cache Service

A FastAPI-based microservice designed to cache and serve social media data from various platforms using MongoDB as the storage backend.

## Features

- Caches social media data from multiple platforms:
  - YouTube
  - Twitter
  - News API
- Supports keyword-based search for cached data
- Provides an API for retrieving cached data
- Optimized database indexing for fast queries

## Tech Stack

- Python 3.11
- FastAPI
- MongoDB
- Docker & Docker Compose
- Motor (Async MongoDB driver)
- Python-decouple (Configuration management)

## Project Structure

```
├── src/
│   ├── application/        # Application business rules
│   │   └── use_cases/      # Use case implementations
│   ├── domain/            # Enterprise business rules
│   │   ├── entities/      # Business entities
│   │   └── repositories/  # Repository interfaces
│   ├── infrastructure/    # Frameworks and drivers
│   │   ├── container.py   # Dependency injection container
│   │   ├── controllers/   # Controllers implementation
│   │   ├── external/      # External services integration
│   │   └── repositories/  # Repository implementations
│   ├── presentation/      # Interface adapters
│   │   └── routes/        # API routes
├── Dockerfile            # Container definition
└── docker-compose.yml    # Service orchestration
```

## Database Collections

The service uses the following MongoDB collections with optimized indexes:

### YouTube Collection
- Indexes:
  - `uid` (unique, ascending)
  - `published_at` (ascending)
  - `keyword` (descending)

### Twitter Collection
- Indexes:
  - `uid` (unique, ascending)
  - `keyword` (descending)
  - `published_at` (ascending)

### News Collection
- Indexes:
  - `uid` (unique, ascending)
  - `keyword` (descending)
  - `published_at` (descending)

### Keyword Search Collection
- Indexes:
  - `keyword` (unique, ascending)

## Setup and Installation

1. Clone the repository

2. Create a `.env` file in the root directory with the following variables:
   ```env
   MONGO_URI=mongodb://mongodb:27017/
   MONGODB=your_database_name
   ```

3. Build and run the services:
   ```bash
   docker-compose up --build
   ```

4. The API will be available at `http://localhost:8000`

## Development

The service is configured for development with auto-reload enabled. The source code is mounted as a volume, so changes will be reflected immediately.

## Contributing

1. Follow the existing code structure and architecture
2. Ensure proper error handling and logging
3. Maintain type hints and documentation
4. Add appropriate indexes for new collections

