version: '3.8'

services:
  cache_db_server:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - mongodb
    volumes:
      - ./src:/app  # Mount src/ directory to /app in container
 

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    

volumes:
  mongo-data: