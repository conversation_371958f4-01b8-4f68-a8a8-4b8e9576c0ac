# Use official Python runtime as base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install dependencies
RUN pip install --no-cache-dir fastapi uvicorn pymongo httpx google-api-python-client python-decouple odmantic

# Copy all files to the container
COPY ./src/ .

# Expose port 8000
EXPOSE 8000

# Command to run the app with auto-reload for development
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]