
services:
 
  agenda-service:
    build:
      context: ./agenda_generator
      dockerfile: ./Dockerfile.gpu
    restart: always
    env_file:
      - ./agenda_generator/.env
    volumes:
      - ./agenda_generator/app:/app/app
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  cache_server:
    build:
      context: ./climas_agenda_cacheserver
      dockerfile: Dockerfile.cloud
    restart: always
    volumes:
      - ./climas_agenda_cacheserver/src:/app
    env_file:
      - ./climas_agenda_cacheserver/.env
    
  